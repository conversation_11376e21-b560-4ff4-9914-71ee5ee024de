## Overview

This guide implements a 7-day trial period system for team accounts in a Next.js 15 + Supabase + Zero Sync Engine application. The implementation is divided into three sequential phases that must be completed in order.

**Trial System Requirements:**

- Auto-start 7-day trials for new team accounts only
- Real-time trial status and state updates via Zero Sync Engine
- Integration with existing billing and subscription systems
- Graceful handling of trial expiration with redirects

---

## Phase 1: Database Implementation

### Objectives

- Add trial fields to accounts table schema
- Create database functions for trial management
- Implement auto-trial triggers for team accounts
- Set up RLS policies for trial data access

### Prerequisites

- Access to Supabase database with mcp 
- Existing accounts table with subscription system
- Current RLS policies for account access

### Implementation Steps

#### 1.1 Schema Modifications

**File Location:**

`apps/web/supabase/migrations/`

1. Create new migration file: `YYYYMMDDHHMMSS_add_trial_system.sql`
2. Add three new columns to accounts table:
    - `trial_started_at` (TIMESTAMPTZ, nullable)
    - `trial_ends_at` (TIMESTAMPTZ, nullable)
    - `trial_status` (TEXT with check constraint)
3. Set default value for `trial_status` as 'inactive'
4. Add check constraint for trial_status values: 'inactive', 'active', 'expired', 'converted'

#### 1.2 Database Functions

**File Location:** Same migration file

1. Create `start_trial(account_id UUID, trial_days INTEGER)` function:
    - Set trial_started_at to current timestamptz
    - Calculate and set trial_ends_at based on trial_days parameter, add 7 days 
    - Update trial_status to 'active'
    - Return success status and calculated end date
2. Create `get_trial_status(account_id UUID)` function:
    - Calculate days remaining from trial_ends_at
    - Determine if trial has expired based on current timestamptz
    - Auto-update trial_status to 'expired' if needed
    - Return comprehensive status object
3. Create `check_trial_expiration()` function:
    - Scan all active trials
    - Update expired trials to 'expired' status
    - Return count of updated records

#### 1.3 Auto-Trial Trigger

**File Location:** Same migration file

1. Create trigger function `auto_start_trial_trigger()`:
    - Execute BEFORE INSERT on accounts table
    - Check if account is team account (is_personal_account = false)
    - Automatically populate trial fields for qualifying accounts
    - Set 7-day trial period as default
2. Create trigger `accounts_auto_trial`:
    - Attach trigger function to accounts table
    - Ensure trigger only fires for INSERT operations

#### 1.4 RLS Policy Updates

**File Location:** Same migration file

1. Create policy `trial_status_read_policy`:
    - Allow users to read trial fields for accounts they're members of
    - Extend existing account access patterns
    - Ensure compatibility with current account_users relationship
2. Update existing account policies if needed:
    - Verify trial fields are included in existing SELECT policies
    - Ensure UPDATE policies allow trial field modifications

#### 1.5 Migration Validation

1. Test migration on development database
2. Verify all constraints and triggers work correctly
3. Confirm RLS policies don't break existing functionality
4. Validate auto-trial creation for new team accounts

### Exit Criteria

- Migration successfully applied to database
- Trial fields added to accounts table
- Database functions created and tested
- Auto-trial trigger working for team account creation
- RLS policies updated and functional

---

## Phase 2: Server Actions & Mutators

### Objectives

- Update Zero schema with trial fields
- Implement client-side mutations for trial management
- Update server-side mutators in push server
- Ensure real-time synchronization of trial data

### Prerequisites

- Phase 1 completed successfully
- Access to Zero schema file
- Access to client and server mutator files
- Understanding of existing mutation patterns

### Implementation Steps

#### 2.1 Zero Schema Updates

**File Location:**

`src/lib/schema.ts`

(or equivalent schema file)

1. Add trial fields to accounts table definition:
    - `trial_started_at` as optional timestamptz
    - `trial_ends_at` as optional timestamptz
    - `trial_status` as enumeration with proper type constraints
2. Ensure field types match database schema:
    - Use timestamptz numbers for date fields
    - Define proper enumeration for trial_status
    - Set appropriate default values
3. Validate schema compilation:
    - Rebuild Zero schema
    - Verify no breaking changes to existing queries
    - Confirm new fields are properly typed

#### 2.2 Client-Side Mutations

**File Location:**

`src/lib/mutators/mutator.ts`

(or equivalent mutators file)

1. Add trial mutations to accounts mutator group:
    - `startTrial` mutation with accountId and optional trialDays parameters
    - `updateTrialStatus` mutation for status changes
    - `convertTrial` mutation for trial-to-paid conversions
2. Implement mutation logic:
    - Calculate timestamptz values for trial dates
    - Update account record with trial information
    - Include updated_at timestamptz updates
    - Ensure proper error handling
3. Add validation:
    - Verify account exists before trial operations
    - Check current trial status before modifications
    - Validate trial date calculations

#### 2.3 Server-Side Mutator Updates

**File Location:**

`src/lib/server-mutators.ts`

(push server)

1. Modify existing accounts.insert mutator:
    - Add trial field initialization logic
    - Auto-start trials for team accounts (is_personal_account = false)
    - Calculate trial end dates server-side
    - Preserve existing account creation logic
2. Add server-side trial mutators:
    - Mirror client-side trial mutations
    - Add server-side validation and business logic
    - Implement proper error handling and logging
    - Ensure database consistency
3. Update mutation permissions:
    - Verify trial mutations respect existing auth patterns
    - Add appropriate permission checks
    - Ensure only authorized users can modify trial status

#### 2.4 Real-time Sync Configuration

**File Location:** Zero server configuration files

1. Verify trial fields are included in sync:
    - Check Zero server picks up schema changes
    - Confirm trial fields sync to clients in real-time
    - Test mutation propagation across connected clients
2. Update any custom sync rules:
    - Ensure trial data follows existing sync patterns
    - Verify no conflicts with existing data flows
    - Test sync performance with new fields

#### 2.5 Integration Testing

1. Test client-side mutations:
    - Verify mutations execute without errors
    - Confirm real-time updates work across clients
    - Test mutation rollback on failures
2. Test server-side integration:
    - Verify auto-trial creation on account insert
    - Test server mutator execution
    - Confirm database consistency
3. Test Zero sync:
    - Verify real-time trial status updates
    - Test cross-client synchronization
    - Confirm no sync conflicts

### Exit Criteria

- Zero schema updated with trial fields
- Client-side trial mutations implemented and working
- Server-side mutators updated with trial logic
- Real-time synchronization working for trial data
- Auto-trial creation working in account creation flow

---

## Phase 3: Frontend Implementation

### Objectives

- Create React hooks for trial status management
- Build UI components for trial display
- Integrate trial system with billing page
- Add trial indicators to dashboard
- Implement trial expiration handling

### Prerequisites

- Phase 1 and 2 completed successfully
- Access to React components and hooks
- Understanding of existing UI patterns
- Access to billing page components

### Implementation Steps

#### 3.1 React Hooks Development

**File Location:**

`apps/web/app/hooks/`

1. Create `use-trial-status.ts` hook:
    - Accept accountId parameter
    - Query account data via Zero client
    - Calculate real-time trial status from trial_ends_at
    - Return comprehensive TrialInfo object
    - Handle auto-status updates for expired trials
2. Create `use-trial-guard.ts` hook:
    - Use useTrialStatus internally
    - Implement automatic redirect logic for expired trials
    - Return trial state for component conditional rendering
    - Handle loading and error states
3. Define TypeScript interfaces:
    - TrialInfo interface with status, days remaining, expiration flags
    - TrialStatus type union for status values
    - Export types for component consumption

#### 3.2 UI Components Development

**File Location:**

`apps/web/app/components/`

1. Create `trial-badge.tsx` component:
    - Small badge showing days remaining
    - Accept accountId and optional className props
    - Use Clock icon with day count
    - Apply destructive styling when ≤2 days remaining
    - Only render when trial is active
2. Create `trial-status-card.tsx` component:
    - Card component for billing page display
    - Show trial progress with visual progress bar
    - Display days remaining or expiration message
    - Include prominent "Upgrade Now" call-to-action button
    - Use orange/warning color scheme for trial state
    - Only render for active or expired trials
3. Create `trial-expiration-modal.tsx` component:
    - Modal for trial expiration notifications
    - Show expiration message and upgrade options
    - Include countdown timer for imminent expiration
    - Provide upgrade and dismiss actions

#### 3.3 Dashboard Integration

**File Location:** `apps/web/app/home/<USER>/` (dashboard components)

1. Update account header component:
    - Import and integrate TrialBadge component
    - Position badge next to account name
    - Ensure responsive design compatibility
    - Maintain existing header functionality
2. Update navigation components:
    - Add trial status indicators where appropriate
    - Ensure trial information doesn't clutter interface
    - Maintain existing navigation patterns

#### 3.4 Billing Page Integration

**File Location:** `apps/web/app/home/<USER>/billing/`

1. Update main billing page component:
    - Import and integrate TrialStatusCard component
    - Position card prominently when trial is active/expired
    - Connect upgrade button to existing checkout flow
    - Ensure card doesn't interfere with existing billing UI
2. Update checkout components:
    - Handle trial-to-paid conversion flow
    - Update trial status to 'converted' on successful payment
    - Maintain existing Stripe integration patterns
    - Preserve existing subscription management logic
3. Update subscription management:
    - Display trial information in subscription details
    - Handle trial conversion in subscription updates
    - Ensure trial data consistency with Stripe data

#### 3.5 Route Protection and Redirects

**File Location:** Various protected route components

1. Implement trial guards on protected routes:
    - Add useTrialGuard to components requiring paid access
    - Configure automatic redirects to billing page
    - Provide grace period for recently expired trials
    - Maintain existing auth protection patterns
2. Update route middleware if needed:
    - Add trial status checks to existing auth middleware
    - Implement server-side trial validation
    - Ensure consistent redirect behavior

#### 3.6 User Experience Enhancements

**File Location:** Various UI components

1. Add trial notifications:
    - Toast notifications for trial milestones (3 days, 1 day remaining)
    - Email notification triggers (if email system exists)
    - In-app notification system integration
2. Update onboarding flow:
    - Add trial explanation to new team account creation
    - Include trial benefits and limitations
    - Guide users through trial features
3. Add trial analytics tracking:
    - Track trial start events
    - Monitor trial conversion rates
    - Log trial expiration and upgrade events

#### 3.7 Error Handling and Edge Cases

1. Handle trial data loading states:
    - Show loading indicators while fetching trial status
    - Handle network errors gracefully
    - Provide fallback UI for trial data failures
2. Handle edge cases:
    - Account switching during active trials
    - Multiple browser sessions with trial updates
    - Trial modifications by multiple team members
    - Server-side trial status conflicts

#### 3.8 Integration Testing

1. Test component rendering:
    - Verify trial components render correctly
    - Test responsive design across devices
    - Confirm accessibility compliance
2. Test user flows:
    - Complete trial creation to conversion flow
    - Test trial expiration and redirect behavior
    - Verify billing page integration works correctly
3. Test real-time updates:
    - Verify trial status updates across browser sessions
    - Test component re-rendering on trial changes
    - Confirm Zero sync integration works properly

### Exit Criteria

- All React hooks implemented and functional
- UI components created and integrated
- Dashboard shows trial status appropriately
- Billing page includes trial management
- Trial expiration handling works correctly
- User experience flows are smooth and intuitive
- Real-time updates work across all components

---

## Final Integration Checklist

### System Integration Points

- Account creation automatically starts trials for team accounts
- Trial status syncs in real-time across all user sessions
- Billing page shows trial information and upgrade options
- Dashboard displays trial status without cluttering interface
- Trial expiration triggers appropriate redirects and notifications
- Existing subscription and billing flows remain unaffected

### Data Consistency

- Database trial data matches Zero sync data
- Trial status calculations are consistent across client and server
- Trial conversions properly update both trial and subscription status
- RLS policies protect trial data appropriately

### User Experience

- Trial system is discoverable but not intrusive
- Trial expiration provides clear upgrade path
- Real-time updates provide immediate feedback
- Error states are handled gracefully
- Performance impact is minimal

This implementation guide provides a comprehensive roadmap for adding trial functionality while maintaining system integrity and user experience quality.