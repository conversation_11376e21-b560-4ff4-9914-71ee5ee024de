# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js

# testing
coverage

# next.js
.next/
out/
next-env.d.ts

# production
build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo

# turbo
.turbo

# ide
.idea/
.vscode/
.zed

# contentlayer
.contentlayer/

#aider
.aider.tags.cache.v4
.aider.input.history
.aider.chat.history.md
.augment/rules/imported/accounts-context.md
.augment/rules/imported/data-fetching.md
.augment/rules/imported/database.md
.augment/rules/imported/forms.md
.augment/rules/imported/jsx.md
.augment/rules/imported/logging.md
.augment/rules/imported/otp.md
.augment/rules/imported/page-creation.md
.augment/rules/imported/permissions.md
.augment/rules/imported/project-structure.md
.augment/rules/imported/react.md
.augment/rules/imported/route-handlers.md
.augment/rules/imported/security.md
.augment/rules/imported/server-actions.md
.augment/rules/imported/super-admin.md
.augment/rules/imported/team-account-context.md
.augment/rules/imported/translations.md
.augment/rules/imported/typescript.md
.augment/rules/imported/ui.md
.augment/rules/imported/zero-sync-engine.md
prd.txt
