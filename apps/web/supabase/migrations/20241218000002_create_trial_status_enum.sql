-- Create enum type for trial status if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'trial_status_enum') THEN
    CREATE TYPE trial_status_enum AS ENUM (
      'inactive',
      'active', 
      'expired',
      'converted'
    );
  END IF;
END $$;

-- Add a comment to document the enum values
COMMENT ON TYPE trial_status_enum IS 'Trial status values: inactive (no trial), active (trial running), expired (trial ended), converted (upgraded to paid)';

-- Grant usage on the enum type immediately after creation
GRANT USAGE ON TYPE trial_status_enum TO authenticated;
GRANT USAGE ON TYPE trial_status_enum TO service_role;

-- Add trial columns if they don't exist
DO $$
BEGIN
  -- Add trial_started_at column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'accounts' 
    AND column_name = 'trial_started_at'
  ) THEN
    ALTER TABLE accounts ADD COLUMN trial_started_at TIMESTAMPTZ;
  END IF;
  
  -- Add trial_ends_at column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'accounts' 
    AND column_name = 'trial_ends_at'
  ) THEN
    ALTER TABLE accounts ADD COLUMN trial_ends_at TIMESTAMPTZ;
  END IF;
END $$;

-- Handle trial_status column conversion to enum
DO $$
BEGIN
  -- Check if trial_status column exists and is not already an enum
  IF EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'accounts' 
    AND column_name = 'trial_status'
    AND data_type != 'USER-DEFINED'
  ) THEN
    -- Add a new column with the enum type
    ALTER TABLE accounts ADD COLUMN trial_status_new trial_status_enum;
    
    -- Copy existing data, converting text to enum
    UPDATE accounts 
    SET trial_status_new = CASE 
      WHEN trial_status = 'inactive' THEN 'inactive'::trial_status_enum
      WHEN trial_status = 'active' THEN 'active'::trial_status_enum
      WHEN trial_status = 'expired' THEN 'expired'::trial_status_enum
      WHEN trial_status = 'converted' THEN 'converted'::trial_status_enum
      ELSE 'inactive'::trial_status_enum -- Default fallback for any unexpected values
    END
    WHERE trial_status IS NOT NULL;
    
    -- Set default value for accounts without trial_status
    UPDATE accounts 
    SET trial_status_new = 'inactive'::trial_status_enum
    WHERE trial_status_new IS NULL;
    
    -- Drop the old column and rename the new one
    ALTER TABLE accounts DROP COLUMN trial_status;
    ALTER TABLE accounts RENAME COLUMN trial_status_new TO trial_status;
    
    -- Set NOT NULL constraint and default value
    ALTER TABLE accounts 
    ALTER COLUMN trial_status SET NOT NULL,
    ALTER COLUMN trial_status SET DEFAULT 'inactive'::trial_status_enum;
    
  ELSIF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'accounts' 
    AND column_name = 'trial_status'
  ) THEN
    -- Column doesn't exist, create it with enum type
    ALTER TABLE accounts ADD COLUMN trial_status trial_status_enum NOT NULL DEFAULT 'inactive'::trial_status_enum;
  END IF;
END $$;

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_accounts_trial_status ON accounts(trial_status);
CREATE INDEX IF NOT EXISTS idx_accounts_trial_ends_at ON accounts(trial_ends_at);

-- Add comments for new columns
COMMENT ON COLUMN accounts.trial_started_at IS 'When the trial period started for this account';
COMMENT ON COLUMN accounts.trial_ends_at IS 'When the trial period ends for this account';
COMMENT ON COLUMN accounts.trial_status IS 'Current status of the trial: inactive, active, expired, converted';

-- Add a check constraint to ensure trial_status is valid (redundant with enum but good for documentation)
ALTER TABLE accounts 
ADD CONSTRAINT check_trial_status_valid 
CHECK (trial_status IN ('inactive', 'active', 'expired', 'converted'));

-- Add comments for documentation
COMMENT ON COLUMN accounts.trial_status IS 'Trial status using enum: inactive (no trial), active (trial running), expired (trial ended), converted (upgraded to paid)';
COMMENT ON CONSTRAINT check_trial_status_valid ON accounts IS 'Ensures trial_status contains only valid enum values';
COMMENT ON INDEX idx_accounts_trial_status IS 'Index for efficient trial status queries';
