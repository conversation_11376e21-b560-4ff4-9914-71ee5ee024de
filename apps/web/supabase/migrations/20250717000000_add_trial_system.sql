/*
 * -------------------------------------------------------
 * Section: Trial System Implementation
 * Add 7-day trial functionality for team accounts
 * -------------------------------------------------------
 */

-- Add trial fields to accounts table
ALTER TABLE public.accounts 
ADD COLUMN trial_started_at TIMESTAMPTZ,
ADD COLUMN trial_ends_at TIMESTAMPTZ,
ADD COLUMN trial_status TEXT DEFAULT 'inactive' NOT NULL;

-- Add check constraint for trial_status values
ALTER TABLE public.accounts 
ADD CONSTRAINT accounts_trial_status_check 
CHECK (trial_status IN ('inactive', 'active', 'expired', 'converted'));

-- Add index for trial status queries
CREATE INDEX IF NOT EXISTS ix_accounts_trial_status ON public.accounts (trial_status);
CREATE INDEX IF NOT EXISTS ix_accounts_trial_ends_at ON public.accounts (trial_ends_at);

-- Add comments for new columns
COMMENT ON COLUMN public.accounts.trial_started_at IS 'When the trial period started for this account';
COMMENT ON COLUMN public.accounts.trial_ends_at IS 'When the trial period ends for this account';
COMMENT ON COLUMN public.accounts.trial_status IS 'Current status of the trial: inactive, active, expired, converted';

/*
 * -------------------------------------------------------
 * Section: Trial Management Functions
 * -------------------------------------------------------
 */

-- Function to start a trial for an account
CREATE OR REPLACE FUNCTION public.start_trial(
    account_id UUID,
    trial_days INTEGER DEFAULT 7
) RETURNS JSON
SET search_path = ''
AS $$
DECLARE
    trial_end_date TIMESTAMPTZ;
    result JSON;
BEGIN
    -- Calculate trial end date
    trial_end_date := CURRENT_TIMESTAMP + (trial_days || ' days')::INTERVAL;
    
    -- Update the account with trial information
    UPDATE public.accounts 
    SET 
        trial_started_at = CURRENT_TIMESTAMP,
        trial_ends_at = trial_end_date,
        trial_status = 'active',
        updated_at = CURRENT_TIMESTAMP
    WHERE id = account_id;
    
    -- Check if update was successful
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Account with ID % not found', account_id;
    END IF;
    
    -- Return success status and calculated end date
    result := json_build_object(
        'success', true,
        'trial_started_at', CURRENT_TIMESTAMP,
        'trial_ends_at', trial_end_date,
        'trial_status', 'active'
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get trial status for an account
CREATE OR REPLACE FUNCTION public.get_trial_status(account_id UUID) 
RETURNS JSON
SET search_path = ''
AS $$
DECLARE
    account_record RECORD;
    days_remaining INTEGER;
    is_expired BOOLEAN;
    result JSON;
BEGIN
    -- Get account trial information
    SELECT 
        trial_started_at,
        trial_ends_at,
        trial_status,
        is_personal_account
    INTO account_record
    FROM public.accounts 
    WHERE id = account_id;
    
    -- Check if account exists
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Account with ID % not found', account_id;
    END IF;
    
    -- Calculate days remaining and expiration status
    IF account_record.trial_ends_at IS NOT NULL THEN
        days_remaining := EXTRACT(DAY FROM (account_record.trial_ends_at - CURRENT_TIMESTAMP));
        is_expired := CURRENT_TIMESTAMP > account_record.trial_ends_at;
        
        -- Auto-update status to expired if needed
        IF is_expired AND account_record.trial_status = 'active' THEN
            UPDATE public.accounts 
            SET 
                trial_status = 'expired',
                updated_at = CURRENT_TIMESTAMP
            WHERE id = account_id;
            account_record.trial_status := 'expired';
        END IF;
    ELSE
        days_remaining := NULL;
        is_expired := FALSE;
    END IF;
    
    -- Build comprehensive status object
    result := json_build_object(
        'trial_status', account_record.trial_status,
        'trial_started_at', account_record.trial_started_at,
        'trial_ends_at', account_record.trial_ends_at,
        'days_remaining', days_remaining,
        'is_expired', is_expired,
        'is_personal_account', account_record.is_personal_account
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check and update expired trials
CREATE OR REPLACE FUNCTION public.check_trial_expiration() 
RETURNS INTEGER
SET search_path = ''
AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    -- Update all active trials that have expired
    UPDATE public.accounts 
    SET 
        trial_status = 'expired',
        updated_at = CURRENT_TIMESTAMP
    WHERE 
        trial_status = 'active' 
        AND trial_ends_at IS NOT NULL 
        AND CURRENT_TIMESTAMP > trial_ends_at;
    
    -- Get count of updated records
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

/*
 * -------------------------------------------------------
 * Section: Auto-Trial Trigger
 * -------------------------------------------------------
 */

-- Trigger function to automatically start trials for team accounts
CREATE OR REPLACE FUNCTION kit.auto_start_trial_trigger() 
RETURNS TRIGGER
SET search_path = ''
AS $$
BEGIN
    -- Check if this is a team account (not personal)
    IF NEW.is_personal_account = FALSE THEN
        -- Set trial fields for 7-day trial
        NEW.trial_started_at := CURRENT_TIMESTAMP;
        NEW.trial_ends_at := CURRENT_TIMESTAMP + INTERVAL '7 days';
        NEW.trial_status := 'active';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for auto-starting trials on team account creation
DROP TRIGGER IF EXISTS accounts_auto_trial ON public.accounts;
CREATE TRIGGER accounts_auto_trial
    BEFORE INSERT ON public.accounts
    FOR EACH ROW
    EXECUTE FUNCTION kit.auto_start_trial_trigger();

/*
 * -------------------------------------------------------
 * Section: RLS Policy Updates
 * -------------------------------------------------------
 */

-- Note: The existing accounts_read policy already covers access to trial fields
-- since it allows users to read accounts they're members of or own.
-- No additional SELECT policies needed for trial fields.

-- Ensure existing UPDATE policy covers trial field updates
-- The accounts_self_update policy already allows primary owners to update their accounts,
-- which includes the new trial fields.

-- Grant necessary permissions for trial functions
GRANT EXECUTE ON FUNCTION public.start_trial(UUID, INTEGER) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.get_trial_status(UUID) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.check_trial_expiration() TO service_role;
