'use client';

import { useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useTrialStatus, type TrialInfo } from './use-trial-status';

export interface UseTrialGuardOptions {
  accountId: string;
  enabled?: boolean;
  redirectOnExpired?: boolean;
  redirectPath?: string;
  allowedStatuses?: Array<'inactive' | 'active' | 'expired' | 'converted'>;
  onTrialExpired?: (trialInfo: TrialInfo) => void;
  onTrialWarning?: (trialInfo: TrialInfo) => void;
  warningThresholdDays?: number;
}

export interface UseTrialGuardReturn {
  trialInfo: TrialInfo | null;
  isLoading: boolean;
  error: Error | null;
  canAccess: boolean;
  shouldShowWarning: boolean;
  shouldRedirect: boolean;
  redirectToUpgrade: () => void;
}

/**
 * Hook to guard routes based on trial status with automatic redirect logic
 * @param options - Configuration options for trial guard behavior
 * @returns Trial guard state and control functions
 */
export function useTrialGuard({
  accountId,
  enabled = true,
  redirectOnExpired = true,
  redirectPath,
  allowedStatuses = ['active', 'converted'],
  onTrialExpired,
  onTrialWarning,
  warningThresholdDays = 2,
}: UseTrialGuardOptions): UseTrialGuardReturn {
  const router = useRouter();
  const { trialInfo, isLoading, error } = useTrialStatus({ accountId, enabled });

  // Determine if user can access the protected content
  const canAccess = !trialInfo || allowedStatuses.includes(trialInfo.status);

  // Determine if we should show a warning (trial ending soon)
  const shouldShowWarning = Boolean(
    trialInfo?.isActive && 
    trialInfo.daysRemaining !== null && 
    trialInfo.daysRemaining <= warningThresholdDays &&
    trialInfo.daysRemaining > 0
  );

  // Determine if we should redirect
  const shouldRedirect = Boolean(
    redirectOnExpired && 
    trialInfo && 
    !canAccess && 
    !isLoading
  );

  // Function to redirect to upgrade/billing page
  const redirectToUpgrade = useCallback(() => {
    const targetPath = redirectPath || `/home/<USER>/billing`;
    router.push(targetPath);
  }, [router, redirectPath, accountId]);

  // Handle trial expiration callback
  useEffect(() => {
    if (trialInfo?.isExpired && onTrialExpired) {
      onTrialExpired(trialInfo);
    }
  }, [trialInfo?.isExpired, onTrialExpired, trialInfo]);

  // Handle trial warning callback
  useEffect(() => {
    if (shouldShowWarning && trialInfo && onTrialWarning) {
      onTrialWarning(trialInfo);
    }
  }, [shouldShowWarning, onTrialWarning, trialInfo]);

  // Auto-redirect on trial expiration
  useEffect(() => {
    if (shouldRedirect) {
      redirectToUpgrade();
    }
  }, [shouldRedirect, redirectToUpgrade]);

  return {
    trialInfo,
    isLoading,
    error,
    canAccess,
    shouldShowWarning,
    shouldRedirect,
    redirectToUpgrade,
  };
}

/**
 * Hook for simple trial access checking without automatic redirects
 * @param accountId - Account ID to check
 * @param requiredStatus - Required trial status for access
 * @returns Simple access check result
 */
export function useTrialAccess(
  accountId: string, 
  requiredStatus: Array<'inactive' | 'active' | 'expired' | 'converted'> = ['active', 'converted']
) {
  const { trialInfo, isLoading, error } = useTrialStatus({ accountId });

  const hasAccess = !trialInfo || requiredStatus.includes(trialInfo.status);

  return {
    hasAccess,
    trialInfo,
    isLoading,
    error,
  };
}

/**
 * Hook to check if an account is eligible for trial features
 * @param accountId - Account ID to check
 * @returns Trial eligibility information
 */
export function useTrialEligibility(accountId: string) {
  const { trialInfo, isLoading, error } = useTrialStatus({ accountId });

  const isEligibleForTrial = Boolean(
    trialInfo && 
    (trialInfo.status === 'inactive' || trialInfo.status === 'active')
  );

  const canStartTrial = Boolean(
    trialInfo && 
    trialInfo.status === 'inactive'
  );

  const canUpgrade = Boolean(
    trialInfo && 
    (trialInfo.status === 'active' || trialInfo.status === 'expired')
  );

  return {
    trialInfo,
    isLoading,
    error,
    isEligibleForTrial,
    canStartTrial,
    canUpgrade,
  };
}

/**
 * Hook for trial-aware feature gating
 * @param accountId - Account ID to check
 * @param featureRequirements - Feature access requirements
 * @returns Feature access information
 */
export function useTrialFeatureGate(
  accountId: string,
  featureRequirements: {
    requiresPaid?: boolean;
    allowDuringTrial?: boolean;
    trialDaysRequired?: number;
  } = {}
) {
  const { 
    requiresPaid = false, 
    allowDuringTrial = true, 
    trialDaysRequired = 0 
  } = featureRequirements;

  const { trialInfo, isLoading, error } = useTrialStatus({ accountId });

  const hasFeatureAccess = (() => {
    if (!trialInfo) return true; // No trial info means full access

    // If feature requires paid subscription
    if (requiresPaid) {
      return trialInfo.isConverted;
    }

    // If feature is allowed during trial
    if (allowDuringTrial) {
      // Check if trial has enough days remaining
      if (trialDaysRequired > 0 && trialInfo.daysRemaining !== null) {
        return trialInfo.daysRemaining >= trialDaysRequired || trialInfo.isConverted;
      }
      
      return trialInfo.isActive || trialInfo.isConverted;
    }

    // Default: only converted accounts have access
    return trialInfo.isConverted;
  })();

  const featureBlockReason = (() => {
    if (hasFeatureAccess) return null;
    if (!trialInfo) return null;

    if (requiresPaid && !trialInfo.isConverted) {
      return 'requires_paid_subscription';
    }

    if (trialInfo.isExpired) {
      return 'trial_expired';
    }

    if (trialDaysRequired > 0 && trialInfo.daysRemaining !== null && trialInfo.daysRemaining < trialDaysRequired) {
      return 'insufficient_trial_time';
    }

    return 'trial_access_denied';
  })();

  return {
    hasFeatureAccess,
    featureBlockReason,
    trialInfo,
    isLoading,
    error,
  };
}
