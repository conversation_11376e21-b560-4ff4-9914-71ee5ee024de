'use client'
import { createColumns } from "./columns";
import CampaignSwitcher from "./campaign-dropdown";
import ChannelSwitcher from "./channel-dropdown";
import { DataTable } from "./data-table";
import {  useZero } from '~/hooks/use-zero';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { CompanyContent } from "~/types/company-content";
import { useEffect, useState, useMemo } from "react";
import { Alert, AlertDescription } from "@kit/ui/alert";
import { Button } from "@kit/ui/button";
import { AlertCircle, RefreshCw } from "lucide-react";
import { If } from "@kit/ui/if";
import { Campaign } from "~/types/Campaign";
import { useTeamAccountWorkspace } from "@kit/team-accounts/hooks/use-team-account-workspace";

export default function TasksDisplayArea() {
  const [filteredContent, setFilteredContent] = useState<CompanyContent[]>([]);
  const [campaignFilteredContent, setCampaignFilteredContent] = useState<CompanyContent[]>([]);
  const [selectedCampaign, setSelectedCampaign] = useState<any>(null);
  const [selectedChannel, setSelectedChannel] = useState<string | null>(null);
  const workspace = useTeamAccountWorkspace();
  const zero = useZero();  

  const [company_content] = useZeroQuery(zero.query.company_content.where('archived', '=', false), {
    ttl: '1d'
  });

  const [campaigns ]= useZeroQuery(
    zero.query.company_campaigns.where('company_id', '=', workspace.account.id),
    {
      ttl: '1d'
    }
  );

  // Get all memberships for this company - this gives us all users in the same company
  const [companyMembers] = useZeroQuery(
    zero.query.accounts_memberships,
    {
      ttl: '1d'
    }
  );

  // Extract unique channels from the currently campaign-filtered content
  const availableChannels = useMemo(() => {
    const uniqueChannels = new Set<string>();
    campaignFilteredContent.forEach((content) => {
      if (content.channel) {
        uniqueChannels.add(content.channel);
      }
    });
    return Array.from(uniqueChannels).sort();
  }, [campaignFilteredContent]);

  const [user_cache] = useZeroQuery(zero.query.user_cache, {
    ttl: '1d'
  });

  // Apply channel filter on top of campaign-filtered content
  useEffect(() => {
    let filtered = [...campaignFilteredContent];
    
    if (selectedChannel) {
      filtered = filtered.filter((content) => content.channel === selectedChannel);
    }
    
    setFilteredContent(filtered);
  }, [campaignFilteredContent, selectedChannel]);

  useEffect(() => {
   if(user_cache && user_cache.length > 0) {
    if(user_cache[0]?.selected_campaign) {
      const filteredContent = company_content.filter((content) => content.campaign_id === user_cache[0]?.selected_campaign);
      const filteredCampaigns = campaigns.filter((campaign) => campaign.id === user_cache[0]?.selected_campaign);
      setSelectedCampaign(filteredCampaigns[0] || null);

      //@ts-expect-error - filteredContent is not typed
      setCampaignFilteredContent(filteredContent);
    } else {
      //@ts-expect-error - filteredContent is not typed
      setCampaignFilteredContent(company_content);
    }
   }
  }, [campaigns, company_content]);

  const updateSelectedCampaign = async (campaign: Campaign) => {
    setSelectedCampaign(campaign);
    // Reset channel filter when campaign changes
    setSelectedChannel(null);
      // Use Zero mutation to create the research record
      console.log('user_cache', workspace.user.id);
      if(user_cache[0]?.user_id) {
      zero.mutate.user_cache.update({
        user_id: workspace.user.id!,
        values: {
          selected_campaign: campaign?.id || null
        }
      });
    }
    const filteredContent = company_content.filter((content) => {
      if(campaign) {
        return content.campaign_id === campaign.id;
      } else {
        return true;
      }
    });
    //@ts-expect-error - filteredContent is not typed
    setCampaignFilteredContent(filteredContent);
  }


  const updateSelectedChannel = (channel: string | null) => {
    setSelectedChannel(channel);
  }

  const handleRetry = async () => {
    zero.mutate.company_campaigns.update({
      id: selectedCampaign?.id,
      values: {
        ...selectedCampaign,
        error_generating: false,
        is_generating: true,
      },
      regenerate: true
    });
    
  }

  const columns = createColumns();
  return (

    <div className="hidden h-full flex-1 flex-col space-y-8 p-8 md:flex">
          <div className="flex flex-col space-y-2">
            <div className="flex flex-row justify-between items-start mb-4">
              <div className="flex flex-row justify-between flex-1 space-y-3 ">
                <div>
                <h3 className="text-lg font-bold tracking-tight">Filter by Campaign</h3>
                <CampaignSwitcher
                  //@ts-expect-error - selectedCampaign is not typed
                  campaigns={campaigns}
                  selectedCampaign={selectedCampaign}
                  //@ts-expect-error - updateSelectedCampaign is not typed
                  setSelectedCampaign={updateSelectedCampaign}
                />
                </div>
                {availableChannels.length > 0 && (
                  <div className="flex flex-col space-y-3">
                    <h3 className="text-lg font-bold tracking-tight">Filter by Channel</h3>
                  <ChannelSwitcher
                    channels={availableChannels}
                    selectedChannel={selectedChannel}
                    setSelectedChannel={updateSelectedChannel}
                  />
                  </div>
                )}
              </div>
              
              <If condition={selectedCampaign?.error_generating}>
                <Alert variant="destructive" className="max-w-sm">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="flex items-center justify-between">
                    <span className="text-sm">Error generating tasks</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleRetry}
                      className="ml-2"
                    >
                      <RefreshCw className="h-3 w-3 mr-1" />
                      Try Again
                    </Button>
                  </AlertDescription>
                </Alert>
              </If>
            </div>
            <div>
              {/* <h2 className="text-2xl font-bold tracking-tight">Welcome back!</h2> */}
              <p className="text-muted-foreground">
                Here&apos;s your list of active marketing tasks.
              </p>
            </div>
          </div>
          <DataTable 
            data={filteredContent || []} 
            columns={columns} 
            isLoading={selectedCampaign?.is_generating}
            loadingMessage={selectedCampaign?.is_generating ? "Your tasks are being generated. Please hold tight, this may take a minute." : undefined}
            />
        </div>
  )
}