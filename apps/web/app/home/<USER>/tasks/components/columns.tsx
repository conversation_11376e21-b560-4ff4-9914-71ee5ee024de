"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Badge } from "@kit/ui/badge"
import { DataTableColumnHeader } from "./data-table-column-header"
import { Checkbox } from "@kit/ui/checkbox"
import { CompanyContent } from "~/types/company-content"
import { Circle } from "lucide-react"
import { AssigneeAvatar } from "./assignee-avatar"

export const createColumns = (): ColumnDef<CompanyContent>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
        className="translate-y-[2px]"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="translate-y-[2px]"
        onClick={(e) => e.stopPropagation()} // Prevent row click when clicking checkbox
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "task_title",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Title" />
    ),
    cell: ({ row }) => {
      return (
        <span className="max-w-[500px] truncate font-medium cursor-pointer">
          {row.getValue("task_title")}
        </span>
      )
    },
  },
  {
    accessorKey: "assigned_to",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Assignee" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex items-center justify-center" onClick={(e) => e.stopPropagation()}>
          <AssigneeAvatar
            taskId={row.original.id}
            assignedTo={row.original.assigned_to}
          />
        </div>
      )
    },
    enableSorting: false,
  },
  {
    accessorKey: "content_type",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Content Type" />
    ),
    cell: ({ row }) => {
      return (
        <Badge variant="outline">{row.original.content_type}</Badge>
      )
    },
    filterFn: (row, id, value) => {
      const rowValue = row.getValue(id) as string;
      return Array.isArray(value) ? value.includes(rowValue) : rowValue === value;
    },
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const status = row.original.status || 'draft';
      
      return (
        <div className="flex w-[100px] items-center">
            <Circle className="mr-2 h-4 w-4 text-muted-foreground" />
          <span>{status.charAt(0).toUpperCase() + status.slice(1)}</span>
        </div>
      )
    },
    filterFn: (row, id, value) => {
      const rowValue = row.getValue(id) as string || 'draft';
      return Array.isArray(value) ? value.includes(rowValue) : rowValue === value;
    },
  },
  {
    accessorKey: "scheduled_publishing_time",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Scheduled Date" />
    ),
    cell: ({ row }) => {
      const scheduledTime = row.original.scheduled_publishing_time;
      if (!scheduledTime) return null;
      
      // Format the date to show only the date part
      const date = new Date(scheduledTime);
      const formattedDate = date.toLocaleDateString();
      
      return (
        <div className="flex items-center">
          <span>{formattedDate}</span>
        </div>
      )
    },
  },
  {
    accessorKey: "updated_at",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Updated" />
    ),
    cell: ({ row }) => {
      const updatedAt = row.original.updated_at;
      if (!updatedAt) return null;
      
      // Format the date and time
      const date = new Date(updatedAt);
      const formattedDate = date.toLocaleDateString();
      const formattedTime = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      
      return (
        <div className="flex items-center">
          <span>{formattedDate} {formattedTime}</span>
        </div>
      )
    },
  },
  // {
  //   accessorKey: "priority",
  //   header: ({ column }) => (
  //     <DataTableColumnHeader column={column} title="Priority" />
  //   ),
  //   cell: ({ row }) => {
  //     const priority = priorities.find(
  //       (priority) => priority.value === row.getValue("priority")
  //     )

  //     if (!priority) {
  //       return null
  //     }

  //     return (
  //       <div className="flex items-center">
  //         {priority.icon && (
  //           <priority.icon className="mr-2 h-4 w-4 text-muted-foreground" />
  //         )}
  //         <span>{priority.label}</span>
  //       </div>
  //     )
  //   },
  //   filterFn: (row, id, value) => {
  //     return value.includes(row.getValue(id))
  //   },
  // },
  // {
  //   id: "actions",
  //   cell: ({ row }) => <DataTableRowActions row={row} />,
  // },
]
