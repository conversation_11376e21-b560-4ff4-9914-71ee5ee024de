import React, { useEffect, useState } from "react";
import { CompanyContent } from "~/types/company-content";
import { Card } from "@kit/ui/card";
import { Avatar } from "@kit/ui/avatar";
import { But<PERSON> } from "@kit/ui/button";
import { UserIcon, X } from "lucide-react";
import { SocialProfile } from "../../index";
import GeneralContentEditor from "./general-content-editor";
import { useTheme } from "next-themes";
import { useZero } from "~/hooks/use-zero";
import { useQuery as useZeroQuery } from "@rocicorp/zero/react";
import { useParams } from "next/navigation";
import PostDialog from "./post-dialog";
import { useImageContent } from "../../context/ContentStudioContext";

// Common component for image display with remove button
function ImageWithRemoveButton({ 
  src, 
  alt, 
  className, 
  onRemove, 
  buttonSize = "default",
  overlay,
  showRemoveButton = true,
  borderColor = "border-gray-200",
  onClick
}: {
  src: string;
  alt: string;
  className?: string;
  onRemove: () => void;
  buttonSize?: "default" | "small";
  overlay?: React.ReactNode;
  showRemoveButton?: boolean;
  borderColor?: string;
  onClick?: () => void;
}) {
  const buttonClasses = buttonSize === "small" 
    ? "absolute top-1 right-1 h-5 w-5 opacity-0 group-hover:opacity-100 transition-opacity"
    : "absolute top-2 right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity";
  
  const iconClasses = buttonSize === "small" ? "h-2 w-2" : "h-3 w-3";

  return (
    <div className="relative group">
      <img 
        src={src} 
        alt={alt}
        className={`${className} ${onClick ? 'cursor-pointer hover:opacity-80 transition-opacity' : ''}`}
        onClick={onClick}
      />
      {showRemoveButton && (
        <Button
          variant="destructive"
          size="icon"
          className={buttonClasses}
          onClick={onRemove}
        >
          <X className={iconClasses} />
        </Button>
      )}
      {overlay}
    </div>
  );
}

// Component that contains the Twitter Preview content
export function TwitterPreview({ selectedProfile }: { selectedProfile?: SocialProfile | null }) {
  const { resolvedTheme } = useTheme();
  const params = useParams();
  const contentId = params.id;
  const zero = useZero();
  const { setSelectedEditorImage, setSelectedImageOption } = useImageContent();

  const [companyContent] = useZeroQuery(
    zero.query.company_content,
    {
      ttl: '10m'
    }
  );

  const selectedCompanyContent = companyContent.filter((content: any) => content.id === contentId)[0];
  
  // Use selectedProfile if available, otherwise fall back to defaults
  const screenName = selectedProfile?.username || "username";
  const name = selectedProfile?.displayName || "Your Name";

  const profilePicture = selectedProfile?.userImage || null;

  // Utility function to detect if URL is a video
  const isVideoUrl = (url: string): boolean => {
    return url.includes('.mp4') || url.includes('.mov') || url.includes('.avi') || url.includes('.webm');
  };

  // Get typed image URLs and separate videos from images
  const allMediaUrls = (selectedCompanyContent?.image_urls as string[]) || [];
  const videoUrls = allMediaUrls.filter(isVideoUrl);
  const imageUrls = allMediaUrls.filter(url => !isVideoUrl(url)).slice(0, 4);
  
  // Show videos if any exist, otherwise show images (Twitter/X only supports one or the other)
  const shouldShowVideo = videoUrls.length > 0;
  const mediaToShow = shouldShowVideo ? videoUrls.slice(0, 1) : imageUrls; // Twitter only supports 1 video

  // Function to remove image from the array
  const removeImage = (urlToRemove: string) => {
    const currentImages = selectedCompanyContent?.image_urls as string[] || [];
    const updatedImages = currentImages.filter((url: string) => url !== urlToRemove);
    
    zero.mutate.company_content.update({
      id: selectedCompanyContent?.id || "",
      values: {
        image_urls: updatedImages
      }
    });
  };

  // Function to handle image click for editing
  const handleImageClick = (imageUrl: string) => {
    // Only allow editing if post is not published
    if (!selectedCompanyContent?.is_published) {
      setSelectedEditorImage(imageUrl);
      setSelectedImageOption('selected');
    }
  };

  // Get current date for display
  const now = new Date();
  const formattedDate = now.toLocaleString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
  
  const formattedTime = now.toLocaleString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });

  // Theme-based styling
  const isDark = resolvedTheme === 'dark';
  const cardBg = isDark ? 'bg-black' : 'bg-white';
  const textColor = isDark ? 'text-white' : 'text-black';
  const mutedTextColor = isDark ? 'text-gray-500' : 'text-gray-500';
  const borderColor = isDark ? 'border-gray-700' : 'border-gray-200';
  const viewsTextColor = isDark ? 'text-white' : 'text-black';

  return (
    <div className="mx-auto max-w-3xl p-6">
      {/* Post Action Section */}
      <div className="mb-4 flex font-bold text-xl w-full">
        {selectedCompanyContent?.is_published ? (
          <div className="text-center  mx-auto">
            <p className="text-gray-600">
              Post is published,{' '}
              <a 
                href={selectedCompanyContent.published_url || '#'} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 underline"
              >
                click here to view
              </a>
            </p>
            <p className="text-gray-500 text-sm mt-1 font-normal">
              cannot edit published posts
            </p>
          </div>
        ) : (
          <div className="text-center flex justify-end w-full">
            <PostDialog companyContent={selectedCompanyContent as unknown as CompanyContent} />
          </div>
        )}
      </div>
      
      {/* Twitter post preview card */}
      <Card className={`w-full ${cardBg} ${textColor} shadow-sm border ${borderColor} rounded-xl overflow-hidden`}>
        <div className="p-4">
          {/* Post header with user info */}
          <div className="flex items-start space-x-3 mb-3">
            <Avatar className={`h-12 w-12 rounded-full border ${borderColor}`}>
              {profilePicture ? (
                <img src={profilePicture} alt={name} />
              ) : (
                <div className="h-12 w-12 rounded-full border bg-gray-200 flex items-center justify-center">
                  <UserIcon className="h-6 w-6 text-gray-500" />
                </div>
              )}
            </Avatar>
            
            <div className="flex-1">
              <div className="flex items-center">
                <h3 className="font-bold text-[15px]">{name}</h3>
                <span className={`${mutedTextColor} text-sm ml-2`}>@{screenName}</span>
                <span className={`ml-2 ${mutedTextColor}`}>·</span>
                <span className={`${mutedTextColor} text-sm ml-2`}>Now</span>
              </div>
              
              {/* Post content */}
              <div className="mt-8 mb-1 whitespace-pre-line text-[15px]">
                <GeneralContentEditor editable={!selectedCompanyContent?.is_published} companyContent={selectedCompanyContent as unknown as CompanyContent} />
              </div>

              {/* Post media - either video OR images */}
              {mediaToShow.length > 0 && (
                <div className="mt-3 mb-3">
                  {shouldShowVideo ? (
                    // Show video
                    <div className={`rounded-xl overflow-hidden border ${borderColor} relative group`}>
                      <video 
                        src={mediaToShow[0]} 
                        className="w-full object-cover"
                        controls
                        loop
                        muted
                      />
                      {!selectedCompanyContent?.is_published && (
                        <Button
                          variant="destructive"
                          size="icon"
                          className="absolute top-2 right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={() => removeImage(mediaToShow[0]!)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  ) : (
                    // Show images
                    <>
                      {imageUrls.length === 1 && (
                        <div className={`rounded-xl overflow-hidden border ${borderColor}`}>
                          <ImageWithRemoveButton
                            src={imageUrls[0]!}
                            alt="Tweet image"
                            className="w-full object-cover"
                            onRemove={() => removeImage(imageUrls[0]!)}
                            showRemoveButton={!selectedCompanyContent?.is_published}
                            borderColor={borderColor}
                            onClick={() => handleImageClick(imageUrls[0]!)}
                          />
                        </div>
                      )}
                      {imageUrls.length === 2 && (
                        <div className={`grid grid-cols-2 gap-1 rounded-xl overflow-hidden border ${borderColor}`}>
                          {imageUrls.map((url, index) => (
                            <ImageWithRemoveButton
                              key={index}
                              src={url}
                              alt={`Tweet image ${index + 1}`}
                              className="w-full h-[200px] object-cover"
                              onRemove={() => removeImage(url)}
                              showRemoveButton={!selectedCompanyContent?.is_published}
                              borderColor={borderColor}
                              onClick={() => handleImageClick(url)}
                            />
                          ))}
                        </div>
                      )}
                      {imageUrls.length === 3 && (
                        <div className={`grid grid-cols-2 gap-1 rounded-xl overflow-hidden border ${borderColor}`}>
                          <ImageWithRemoveButton
                            src={imageUrls[0]!}
                            alt="Tweet image 1"
                            className="w-full h-[200px] object-cover row-span-2"
                            onRemove={() => removeImage(imageUrls[0]!)}
                            showRemoveButton={!selectedCompanyContent?.is_published}
                            borderColor={borderColor}
                            onClick={() => handleImageClick(imageUrls[0]!)}
                          />
                          <div className="grid grid-rows-2 gap-1">
                            <ImageWithRemoveButton
                              src={imageUrls[1]!}
                              alt="Tweet image 2"
                              className="w-full h-[99px] object-cover"
                              onRemove={() => removeImage(imageUrls[1]!)}
                              buttonSize="small"
                              showRemoveButton={!selectedCompanyContent?.is_published}
                              borderColor={borderColor}
                              onClick={() => handleImageClick(imageUrls[1]!)}
                            />
                            <ImageWithRemoveButton
                              src={imageUrls[2]!}
                              alt="Tweet image 3"
                              className="w-full h-[99px] object-cover"
                              onRemove={() => removeImage(imageUrls[2]!)}
                              buttonSize="small"
                              showRemoveButton={!selectedCompanyContent?.is_published}
                              borderColor={borderColor}
                              onClick={() => handleImageClick(imageUrls[2]!)}
                            />
                          </div>
                        </div>
                      )}
                      {imageUrls.length === 4 && (
                        <div className={`grid grid-cols-2 gap-1 rounded-xl overflow-hidden border ${borderColor}`}>
                          <ImageWithRemoveButton
                            src={imageUrls[0]!}
                            alt="Tweet image 1"
                            className="w-full h-[200px] object-cover"
                            onRemove={() => removeImage(imageUrls[0]!)}
                            showRemoveButton={!selectedCompanyContent?.is_published}
                            borderColor={borderColor}
                            onClick={() => handleImageClick(imageUrls[0]!)}
                          />
                          <ImageWithRemoveButton
                            src={imageUrls[1]!}
                            alt="Tweet image 2"
                            className="w-full h-[200px] object-cover"
                            onRemove={() => removeImage(imageUrls[1]!)}
                            showRemoveButton={!selectedCompanyContent?.is_published}
                            borderColor={borderColor}
                            onClick={() => handleImageClick(imageUrls[1]!)}
                          />
                          <ImageWithRemoveButton
                            src={imageUrls[2]!}
                            alt="Tweet image 3"
                            className="w-full h-[200px] object-cover"
                            onRemove={() => removeImage(imageUrls[2]!)}
                            showRemoveButton={!selectedCompanyContent?.is_published}
                            borderColor={borderColor}
                            onClick={() => handleImageClick(imageUrls[2]!)}
                          />
                          <ImageWithRemoveButton
                            src={imageUrls[3]!}
                            alt="Tweet image 4"
                            className="w-full h-[200px] object-cover"
                            onRemove={() => removeImage(imageUrls[3]!)}
                            showRemoveButton={!selectedCompanyContent?.is_published}
                            borderColor={borderColor}
                            onClick={() => handleImageClick(imageUrls[3]!)}
                          />
                        </div>
                      )}
                    </>
                  )}
                </div>
              )}

              {/* Post time */}
              <div className={`${mutedTextColor} text-sm mt-2`}>
                {formattedTime} · {formattedDate} · <span className={`${viewsTextColor} font-bold`}>4,968 </span> Views
              </div>
              
              {/* Engagement stats */}
              <div className={`flex justify-between items-center py-3 border-t ${borderColor} mt-3`}>
                <div className="flex space-x-12">
                  <div className={`flex items-center ${mutedTextColor} hover:text-blue-400`}>
                    <svg viewBox="0 0 24 24" width="18" height="18" fill="currentColor">
                      <path d="M1.751 10c0-4.42 3.584-8 8.005-8h4.366c4.49 0 8.129 3.64 8.129 8.13 0 2.96-1.607 5.68-4.196 7.11l-8.054 4.46v-3.69h-.067c-4.49.1-8.183-3.51-8.183-8.01zm8.005-6c-3.317 0-6.005 2.69-6.005 6 0 3.37 2.77 6.08 6.138 6.01l.351-.01h1.761v2.3l5.087-2.81c1.951-1.08 3.163-3.13 3.163-5.36 0-3.39-2.744-6.13-6.129-6.13H9.756z"></path>
                    </svg>
                    <span className="ml-1">0</span>
                  </div>
                  <div className={`flex items-center ${mutedTextColor} hover:text-green-400`}>
                    <svg viewBox="0 0 24 24" width="18" height="18" fill="currentColor">
                      <path d="M4.5 3.88l4.432 4.14-1.364 1.46L5.5 7.55V16c0 1.1.896 2 2 2H13v2H7.5c-2.209 0-4-1.79-4-4V7.55L1.432 9.48.068 8.02 4.5 3.88zM16.5 6H11V4h5.5c2.209 0 4 1.79 4 4v8.45l2.068-1.93 1.364 1.46-4.432 4.14-4.432-4.14 1.364-1.46 2.068 1.93V8c0-1.1-.896-2-2-2z"></path>
                    </svg>
                    <span className="ml-1">0</span>
                  </div>
                  <div className={`flex items-center ${mutedTextColor} hover:text-red-400`}>
                    <svg viewBox="0 0 24 24" width="18" height="18" fill="currentColor">
                      <path d="M16.697 5.5c-1.222-.06-2.679.51-3.89 2.16l-.805 1.09-.806-1.09C9.984 6.01 8.526 5.44 7.304 5.5c-1.243.07-2.349.78-2.91 1.91-.552 1.12-.633 2.78.479 4.82 1.074 1.97 3.257 4.27 7.129 6.61 3.87-2.34 6.052-4.64 7.126-6.61 1.111-2.04 1.03-3.7.477-4.82-.561-1.13-1.666-1.84-2.908-1.91zm4.187 7.69c-1.351 2.48-4.001 5.12-8.379 7.67l-.503.3-.504-.3c-4.379-2.55-7.029-5.19-8.382-7.67-1.36-2.5-1.41-4.86-.514-6.67.887-1.79 2.647-2.91 4.601-3.01 1.651-.09 3.368.56 4.798 2.01 1.429-1.45 3.146-2.1 4.796-2.01 1.954.1 3.714 1.22 4.601 3.01.896 1.81.846 4.17-.514 6.67z"></path>
                    </svg>
                    <span className="ml-1">0</span>
                  </div>
                  <div className={`flex items-center ${mutedTextColor} hover:text-blue-400`}>
                    <svg viewBox="0 0 24 24" width="18" height="18" fill="currentColor">
                      <path d="M12 2.59l5.7 5.7-1.41 1.42L13 6.41V16h-2V6.41l-3.3 3.3-1.41-1.42L12 2.59zM21 15l-.02 3.51c0 1.38-1.12 2.49-2.5 2.49H5.5C4.11 21 3 19.88 3 18.5V15h2v3.5c0 .28.22.5.5.5h12.98c.28 0 .5-.22.5-.5L19 15h2z"></path>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}
