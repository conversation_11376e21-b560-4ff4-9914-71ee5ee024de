import React, { useEffect, useState } from "react";
import { CompanyContent } from "~/types/company-content";
import { Card } from "@kit/ui/card";
import { Avatar } from "@kit/ui/avatar";
import { <PERSON><PERSON> } from "@kit/ui/button";
import { UserIcon, X, Pause, Play, Clock } from "lucide-react";
import { SocialProfile } from "../../index";
import GeneralContentEditor from "./general-content-editor";
import { useTheme } from "next-themes";
import { useZero } from "~/hooks/use-zero";
import { useQuery as useZeroQuery } from "@rocicorp/zero/react";
import { useParams } from "next/navigation";
import PostDialog from "./post-dialog";
import { useImageContent } from "../../context/ContentStudioContext";
import { toast } from "@kit/ui/sonner";
import { useTeamAccountWorkspace } from "@kit/team-accounts/hooks/use-team-account-workspace";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON><PERSON>ead<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@kit/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@kit/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@kit/ui/select";
import { Input } from "@kit/ui/input";
import { Calendar } from "@kit/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@kit/ui/popover";
import { CalendarIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { format } from "date-fns";
import { cn } from "@kit/ui/utils";
import { useAyrshareStatus } from "../../../../../../../../hooks/use-ayrshare-status";

// Schema for unpause dialog
const UnpauseScheduleSchema = z.object({
  scheduleDate: z.date(),
  scheduleTime: z.string(),
  timezone: z.string()
});

type UnpauseScheduleData = z.infer<typeof UnpauseScheduleSchema>;

// Comprehensive timezone options
const TIMEZONE_OPTIONS = [
  // UTC and GMT
  { value: 'UTC', label: 'UTC (GMT+0)' },
  { value: 'GMT', label: 'GMT (GMT+0)' },
  
  // Americas
  { value: 'America/Los_Angeles', label: 'Pacific Time (GMT-8/-7)' },
  { value: 'America/Denver', label: 'Mountain Time (GMT-7/-6)' },
  { value: 'America/Phoenix', label: 'Arizona (GMT-7)' },
  { value: 'America/Chicago', label: 'Central Time (GMT-6/-5)' },
  { value: 'America/New_York', label: 'Eastern Time (GMT-5/-4)' },
  { value: 'America/Halifax', label: 'Atlantic Time (GMT-4/-3)' },
  { value: 'America/St_Johns', label: 'Newfoundland (GMT-3:30/-2:30)' },
  { value: 'America/Sao_Paulo', label: 'São Paulo (GMT-3/-2)' },
  { value: 'America/Argentina/Buenos_Aires', label: 'Buenos Aires (GMT-3)' },
  { value: 'America/Noronha', label: 'Fernando de Noronha (GMT-2)' },
  { value: 'Atlantic/Cape_Verde', label: 'Cape Verde (GMT-1)' },
  
  // Europe & Africa
  { value: 'Europe/London', label: 'London (GMT+0/+1)' },
  { value: 'Europe/Dublin', label: 'Dublin (GMT+0/+1)' },
  { value: 'Europe/Paris', label: 'Paris (GMT+1/+2)' },
  { value: 'Europe/Berlin', label: 'Berlin (GMT+1/+2)' },
  { value: 'Europe/Rome', label: 'Rome (GMT+1/+2)' },
  { value: 'Europe/Madrid', label: 'Madrid (GMT+1/+2)' },
  { value: 'Europe/Amsterdam', label: 'Amsterdam (GMT+1/+2)' },
  { value: 'Europe/Stockholm', label: 'Stockholm (GMT+1/+2)' },
  { value: 'Europe/Athens', label: 'Athens (GMT+2/+3)' },
  { value: 'Europe/Helsinki', label: 'Helsinki (GMT+2/+3)' },
  { value: 'Europe/Kiev', label: 'Kyiv (GMT+2/+3)' },
  { value: 'Europe/Istanbul', label: 'Istanbul (GMT+3)' },
  { value: 'Europe/Moscow', label: 'Moscow (GMT+3)' },
  { value: 'Africa/Cairo', label: 'Cairo (GMT+2/+3)' },
  { value: 'Africa/Johannesburg', label: 'Johannesburg (GMT+2)' },
  { value: 'Africa/Lagos', label: 'Lagos (GMT+1)' },
  { value: 'Africa/Casablanca', label: 'Casablanca (GMT+0/+1)' },
  
  // Middle East & Central Asia
  { value: 'Asia/Kuwait', label: 'Kuwait (GMT+3)' },
  { value: 'Asia/Riyadh', label: 'Riyadh (GMT+3)' },
  { value: 'Asia/Baghdad', label: 'Baghdad (GMT+3)' },
  { value: 'Asia/Tehran', label: 'Tehran (GMT+3:30/+4:30)' },
  { value: 'Asia/Dubai', label: 'Dubai (GMT+4)' },
  { value: 'Asia/Baku', label: 'Baku (GMT+4)' },
  { value: 'Asia/Kabul', label: 'Kabul (GMT+4:30)' },
  { value: 'Asia/Karachi', label: 'Karachi (GMT+5)' },
  { value: 'Asia/Kolkata', label: 'Mumbai/Delhi (GMT+5:30)' },
  { value: 'Asia/Kathmandu', label: 'Kathmandu (GMT+5:45)' },
  { value: 'Asia/Dhaka', label: 'Dhaka (GMT+6)' },
  { value: 'Asia/Yangon', label: 'Yangon (GMT+6:30)' },
  
  // East Asia
  { value: 'Asia/Bangkok', label: 'Bangkok (GMT+7)' },
  { value: 'Asia/Ho_Chi_Minh', label: 'Ho Chi Minh City (GMT+7)' },
  { value: 'Asia/Jakarta', label: 'Jakarta (GMT+7)' },
  { value: 'Asia/Shanghai', label: 'Shanghai (GMT+8)' },
  { value: 'Asia/Beijing', label: 'Beijing (GMT+8)' },
  { value: 'Asia/Hong_Kong', label: 'Hong Kong (GMT+8)' },
  { value: 'Asia/Singapore', label: 'Singapore (GMT+8)' },
  { value: 'Asia/Manila', label: 'Manila (GMT+8)' },
  { value: 'Asia/Seoul', label: 'Seoul (GMT+9)' },
  { value: 'Asia/Tokyo', label: 'Tokyo (GMT+9)' },
  { value: 'Australia/Adelaide', label: 'Adelaide (GMT+9:30/+10:30)' },
  { value: 'Australia/Darwin', label: 'Darwin (GMT+9:30)' },
  
  // Pacific
  { value: 'Australia/Sydney', label: 'Sydney (GMT+10/+11)' },
  { value: 'Australia/Melbourne', label: 'Melbourne (GMT+10/+11)' },
  { value: 'Australia/Brisbane', label: 'Brisbane (GMT+10)' },
  { value: 'Pacific/Guam', label: 'Guam (GMT+10)' },
  { value: 'Asia/Vladivostok', label: 'Vladivostok (GMT+10)' },
  { value: 'Pacific/Auckland', label: 'Auckland (GMT+12/+13)' },
  { value: 'Pacific/Fiji', label: 'Fiji (GMT+12/+13)' },
  { value: 'Pacific/Tongatapu', label: 'Tonga (GMT+13)' },
  { value: 'Pacific/Honolulu', label: 'Hawaii (GMT-10)' },
  { value: 'Pacific/Marquesas', label: 'Marquesas (GMT-9:30)' },
  { value: 'America/Anchorage', label: 'Alaska (GMT-9/-8)' },
];

// Function to detect user's timezone
const getUserTimezone = (): string => {
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch (error) {
    return 'UTC';
  }
};

// Common component for image display with remove button
function ImageWithRemoveButton({ 
  src, 
  alt, 
  className, 
  onRemove, 
  buttonSize = "default",
  overlay,
  showRemoveButton = true,
  borderColor = "border-gray-200",
  onClick
}: {
  src: string;
  alt: string;
  className?: string;
  onRemove: () => void;
  buttonSize?: "default" | "small";
  overlay?: React.ReactNode;
  showRemoveButton?: boolean;
  borderColor?: string;
  onClick?: () => void;
}) {
  const buttonClasses = buttonSize === "small" 
    ? "absolute top-1 right-1 h-5 w-5 opacity-0 group-hover:opacity-100 transition-opacity"
    : "absolute top-2 right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity";
  
  const iconClasses = buttonSize === "small" ? "h-2 w-2" : "h-3 w-3";

  return (
    <div className="relative group">
      <img 
        src={src} 
        alt={alt}
        className={`${className} ${onClick ? 'cursor-pointer hover:opacity-80 transition-opacity' : ''}`}
        onClick={onClick}
      />
      {showRemoveButton && (
        <Button
          variant="destructive"
          size="icon"
          className={buttonClasses}
          onClick={onRemove}
        >
          <X className={iconClasses} />
        </Button>
      )}
      {overlay}
    </div>
  );
}

// Component for unpause dialog with time selection
function UnpauseDialog({ 
  companyContent, 
  selectedProfile, 
  onUnpause 
}: { 
  companyContent: CompanyContent;
  selectedProfile?: SocialProfile | null;
  onUnpause: (newScheduleDate: string) => void;
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  
  // Get user's timezone as default
  const userTimezone = getUserTimezone();
  const defaultTimezone = TIMEZONE_OPTIONS.find(tz => tz.value === userTimezone)?.value || 'UTC';
  
  // Parse existing schedule date to set defaults
  const existingScheduleDate = companyContent.schedule_date ? new Date(companyContent.schedule_date) : new Date();
  const existingTime = existingScheduleDate.toLocaleTimeString('en-US', { hour12: false, hour: '2-digit', minute: '2-digit' });
  
  const form = useForm<UnpauseScheduleData>({
    resolver: zodResolver(UnpauseScheduleSchema),
    defaultValues: {
      scheduleDate: existingScheduleDate,
      scheduleTime: existingTime,
      timezone: defaultTimezone,
    },
  });

  const watchScheduleDate = form.watch("scheduleDate");
  const watchScheduleTime = form.watch("scheduleTime");
  
  // Check if selected date/time is in the past
  const isPastDateTime = () => {
    if (!watchScheduleDate || !watchScheduleTime) return false;
    
    const timeParts = watchScheduleTime.split(':');
    const hours = parseInt(timeParts[0] || '0', 10);
    const minutes = parseInt(timeParts[1] || '0', 10);
    
    const selectedDateTime = new Date(watchScheduleDate);
    selectedDateTime.setHours(hours, minutes, 0, 0);
    
    return selectedDateTime < new Date();
  };

  const onSubmit = async (data: UnpauseScheduleData) => {
    setIsProcessing(true);
    
    try {
      // Parse the time (HH:MM format)
      const timeParts = data.scheduleTime.split(':');
      const hours = parseInt(timeParts[0] || '0', 10);
      const minutes = parseInt(timeParts[1] || '0', 10);
      
      // Create date object with selected date and time
      const scheduledDateTime = new Date(data.scheduleDate);
      scheduledDateTime.setHours(hours, minutes, 0, 0);
      
      // Convert to UTC for Ayrshare API
      const scheduleDate = scheduledDateTime.toISOString();
      
      await onUnpause(scheduleDate);
      setIsOpen(false);
      form.reset();
    } catch (error) {
      console.error('Error in unpause form:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center space-x-1">
          <Play className="h-3 w-3" />
          <span>Resume</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Resume Scheduled Post</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            
            {/* Date Picker */}
            <FormField
              control={form.control}
              name="scheduleDate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Schedule Date</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Time Picker */}
            <FormField
              control={form.control}
              name="scheduleTime"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Schedule Time</FormLabel>
                  <FormControl>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <Input
                        type="time"
                        className="flex-1"
                        {...field}
                        placeholder="HH:MM"
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Timezone Selector */}
            <FormField
              control={form.control}
              name="timezone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Timezone</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select timezone" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {TIMEZONE_OPTIONS.map((timezone) => (
                        <SelectItem key={timezone.value} value={timezone.value}>
                          {timezone.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Warning for past dates */}
            {isPastDateTime() && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <p className="text-yellow-800 font-medium">Past Date Selected</p>
                </div>
                <p className="text-yellow-700 text-sm mt-1">
                  The selected date and time is in the past. The post will be published immediately when resumed.
                </p>
              </div>
            )}

            {/* Submit Button */}
            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={() => setIsOpen(false)} disabled={isProcessing}>
                Cancel
              </Button>
              <Button type="submit" disabled={isProcessing}>
                {isProcessing ? 'Processing...' : 'Resume Post'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

// Component for scheduled post actions
function ScheduledPostActions({ 
  companyContent, 
  selectedProfile 
}: { 
  companyContent: CompanyContent;
  selectedProfile?: SocialProfile | null;
}) {
  const [isToggling, setIsToggling] = useState(false);
  const workspace = useTeamAccountWorkspace();
  const zero = useZero();
  
  // Use Ayrshare status as source of truth
  const { 
    status: ayrshareStatus, 
    isLoading: isStatusLoading, 
    error: statusError,
    refetch: refetchStatus 
  } = useAyrshareStatus(
    companyContent.ayrshare_post_id, 
    selectedProfile?.profile_key,
    { enabled: !!companyContent.ayrshare_post_id && !!selectedProfile?.profile_key }
  );

  const handlePause = async () => {
    console.log('🔍 Debug pause attempt:', {
      ayrshare_post_id: companyContent.ayrshare_post_id,
      selectedProfile: selectedProfile,
      companyContent: companyContent
    });
    
    if (!companyContent.ayrshare_post_id || !selectedProfile) {
      console.log('❌ Missing data:', {
        hasPostId: !!companyContent.ayrshare_post_id,
        hasProfile: !!selectedProfile,
        postId: companyContent.ayrshare_post_id,
        profile: selectedProfile
      });
      toast.error('Unable to pause post: Missing post ID or profile');
      return;
    }

    setIsToggling(true);
    
    try {
      const response = await fetch('/api/integrations/ayrshare/post', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ayrsharePostId: companyContent.ayrshare_post_id,
          action: 'pause',
          companyId: workspace.account.id,
          profileKey: selectedProfile.profile_key,
          contentId: companyContent.id
        })
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success('Post paused successfully');
        
        // Refetch status from Ayrshare to get updated state
        await refetchStatus();
      } else {
        toast.error(result.error || 'Failed to pause post');
      }
    } catch (error) {
      console.error('Error pausing post:', error);
      toast.error('Failed to pause post');
    } finally {
      setIsToggling(false);
    }
  };

  const handleUnpause = async (newScheduleDate: string) => {
    if (!companyContent.ayrshare_post_id || !selectedProfile) {
      toast.error('Unable to unpause post: Missing post ID or profile');
      return;
    }

    setIsToggling(true);
    
    try {
      const response = await fetch('/api/integrations/ayrshare/post', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ayrsharePostId: companyContent.ayrshare_post_id,
          action: 'unpause',
          companyId: workspace.account.id,
          profileKey: selectedProfile.profile_key,
          contentId: companyContent.id,
          newScheduleDate: newScheduleDate
        })
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success('Post resumed successfully');
        
        // Refetch status from Ayrshare to get updated state
        await refetchStatus();
      } else {
        toast.error(result.error || 'Failed to resume post');
      }
    } catch (error) {
      console.error('Error resuming post:', error);
      toast.error('Failed to resume post');
    } finally {
      setIsToggling(false);
    }
  };

  // Determine status from Ayrshare or fallback to database
  const isPaused = ayrshareStatus?.status === 'paused';
  const isPublished = ayrshareStatus?.status === 'success';
  const isPending = ayrshareStatus?.status === 'pending';
  
  // Format the schedule date - use Ayrshare data if available, otherwise database
  const scheduleDate = ayrshareStatus?.scheduleDate 
    ? new Date(ayrshareStatus.scheduleDate) 
    : companyContent.schedule_date 
    ? new Date(companyContent.schedule_date) 
    : null;
  const formattedDate = scheduleDate ? scheduleDate.toLocaleString() : 'Unknown date';

  // Show loading state if fetching status
  if (isStatusLoading) {
    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          <p className="text-blue-900">Checking post status...</p>
        </div>
      </div>
    );
  }

  // Show error state if failed to fetch status
  if (statusError) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <CalendarIcon className="h-5 w-5 text-yellow-600" />
            <div>
              <p className="font-medium text-yellow-900">Unable to check post status</p>
              <p className="text-sm text-yellow-700">Using local data: {companyContent.is_paused ? 'Paused' : 'Scheduled'}</p>
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetchStatus()}
          >
            Retry
          </Button>
        </div>
      </div>
    );
  }

  // Show published state if post is published
  if (isPublished) {
    const publishedUrl = ayrshareStatus?.postUrls 
      ? Object.values(ayrshareStatus.postUrls)[0] 
      : companyContent.published_url;
      
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <p className="text-green-800 font-medium">Post is published</p>
          </div>
          <div className="flex items-center space-x-2">
            {publishedUrl && (
              <Button
                variant="outline"
                size="sm"
                asChild
              >
                <a 
                  href={publishedUrl} 
                  target="_blank" 
                  rel="noopener noreferrer"
                >
                  View Post
                </a>
              </Button>
            )}
          </div>
        </div>
        <p className="text-green-700 text-sm mt-1">
          Published posts cannot be edited
        </p>
      </div>
    );
  }

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <CalendarIcon className="h-5 w-5 text-blue-600" />
          <div>
            <p className="font-medium text-blue-900">
              {isPaused ? 'Scheduled Post (Paused)' : 'Scheduled Post'}
            </p>
            <p className="text-sm text-blue-700 flex items-center space-x-1">
              <Clock className="h-3 w-3" />
              <span>Scheduled for: {formattedDate}</span>
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {isPaused ? (
            <UnpauseDialog 
              companyContent={companyContent}
              selectedProfile={selectedProfile}
              onUnpause={handleUnpause}
            />
          ) : (
            <Button
              variant="outline"
              size="sm"
              onClick={handlePause}
              disabled={isToggling}
              className="flex items-center space-x-1"
            >
              {isToggling ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
                  <span>Processing...</span>
                </>
              ) : (
                <>
                  <Pause className="h-3 w-3" />
                  <span>Pause</span>
                </>
              )}
            </Button>
          )}
          {companyContent.published_url && (
            <Button
              variant="outline"
              size="sm"
              asChild
            >
              <a 
                href={companyContent.published_url} 
                target="_blank" 
                rel="noopener noreferrer"
              >
                View Post
              </a>
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}

// Component that contains the Twitter Preview content
export function TwitterPreview({ selectedProfile }: { selectedProfile?: SocialProfile | null }) {
  const { resolvedTheme } = useTheme();
  const params = useParams();
  const contentId = params.id;
  const zero = useZero();
  const { setSelectedEditorImage, setSelectedImageOption } = useImageContent();

  const [companyContent] = useZeroQuery(
    zero.query.company_content,
    {
      ttl: '10m'
    }
  );

  const selectedCompanyContent = companyContent.filter((content: any) => content.id === contentId)[0];
  
  // Use Ayrshare status for posts that have been scheduled through Ayrshare
  const profileKey = (selectedProfile?.profile_key || undefined) as string | undefined;
  const ayrsharePostId = selectedCompanyContent?.ayrshare_post_id || undefined;
  
  console.log('🔍 Twitter Ayrshare Debug:', {
    contentId: selectedCompanyContent?.id,
    ayrsharePostId,
    profileKey,
    dbIsScheduled: selectedCompanyContent?.is_scheduled,
    dbIsPublished: selectedCompanyContent?.is_published,
    enabledCheck: !!ayrsharePostId && !!profileKey
  });
  
  const { 
    status: ayrshareStatus, 
    isLoading: isStatusLoading,
    error: statusError
  } = useAyrshareStatus(
    ayrsharePostId, 
    profileKey,
    { 
      enabled: !!ayrsharePostId && !!profileKey,
      refetchInterval: 30000 // Check every 30 seconds
    }
  );
  
  console.log('📊 Ayrshare Status Result:', {
    ayrshareStatus,
    isStatusLoading,
    statusError
  });
  
  // Determine the actual post status
  // A post is published if Ayrshare status is 'success' AND type is NOT 'scheduled'
  const isPublished = (ayrshareStatus?.status === 'success' && ayrshareStatus?.type !== 'scheduled') || 
                     selectedCompanyContent?.is_published;
  const isScheduled = (ayrshareStatus?.status === 'pending' || ayrshareStatus?.status === 'paused' || 
                      (ayrshareStatus?.status === 'success' && ayrshareStatus?.type === 'scheduled')) || 
                     (selectedCompanyContent?.is_scheduled && !isPublished);
                     
  console.log('🎯 Final Status Decision:', {
    ayrshareStatus: ayrshareStatus?.status,
    isPublished,
    isScheduled,
    dbScheduled: selectedCompanyContent?.is_scheduled,
    dbPublished: selectedCompanyContent?.is_published
  });
  
  // Optional: Sync database when Ayrshare status changes
  React.useEffect(() => {
    if (!ayrshareStatus || !selectedCompanyContent?.id || !ayrsharePostId) return;
    
    const shouldUpdateDB = 
      (ayrshareStatus.status === 'success' && ayrshareStatus.type !== 'scheduled' && !selectedCompanyContent.is_published) ||
      (ayrshareStatus.status === 'pending' && selectedCompanyContent.is_published);
    
    if (shouldUpdateDB) {
      console.log('🔄 Syncing database with Ayrshare status:', ayrshareStatus.status);
      
      const updateValues: any = {};
      
      if (ayrshareStatus.status === 'success') {
        // Post was published by Ayrshare
        updateValues.is_published = true;
        updateValues.is_scheduled = false;
        updateValues.published_at = Date.now();
        
        // Get published URL from Ayrshare if available
        if (ayrshareStatus.postIds?.[0]?.postUrl && !selectedCompanyContent.published_url) {
          updateValues.published_url = ayrshareStatus.postIds[0].postUrl;
        } else if (ayrshareStatus.postUrls && !selectedCompanyContent.published_url) {
          const firstUrl = Object.values(ayrshareStatus.postUrls)[0];
          if (firstUrl) {
            updateValues.published_url = firstUrl;
          }
        }
      }
      
      // Update the database
      try {
        zero.mutate.company_content.update({
          id: selectedCompanyContent.id,
          values: updateValues
        });
      } catch (error) {
        console.error('Failed to sync database with Ayrshare status:', error);
      }
    }
  }, [ayrshareStatus?.status, selectedCompanyContent?.id, selectedCompanyContent?.is_published, ayrsharePostId, zero]);
  
  // Use selectedProfile if available, otherwise fall back to defaults
  const screenName = selectedProfile?.username || "username";
  const name = selectedProfile?.display_name || "Your Name";

  const profilePicture = selectedProfile?.user_image || null;

  // Utility function to detect if URL is a video
  const isVideoUrl = (url: string): boolean => {
    return url.includes('.mp4') || url.includes('.mov') || url.includes('.avi') || url.includes('.webm');
  };

  // Get typed image URLs and separate videos from images
  const allMediaUrls = (selectedCompanyContent?.image_urls as string[]) || [];
  const videoUrls = allMediaUrls.filter(isVideoUrl);
  const imageUrls = allMediaUrls.filter(url => !isVideoUrl(url)).slice(0, 4);
  
  // Show videos if any exist, otherwise show images (Twitter/X only supports one or the other)
  const shouldShowVideo = videoUrls.length > 0;
  const mediaToShow = shouldShowVideo ? videoUrls.slice(0, 1) : imageUrls; // Twitter only supports 1 video

  // Function to remove image from the array
  const removeImage = (urlToRemove: string) => {
    const currentImages = selectedCompanyContent?.image_urls as string[] || [];
    const updatedImages = currentImages.filter((url: string) => url !== urlToRemove);
    
    zero.mutate.company_content.update({
      id: selectedCompanyContent?.id || "",
      values: {
        image_urls: updatedImages
      }
    });
  };

  // Function to handle image click for editing
  const handleImageClick = (imageUrl: string) => {
    // Only allow editing if post is not published and not scheduled
    if (!selectedCompanyContent?.is_published && !selectedCompanyContent?.is_scheduled) {
      setSelectedEditorImage(imageUrl);
      setSelectedImageOption('selected');
    }
  };

  // Get current date for display
  const now = new Date();
  const formattedDate = now.toLocaleString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
  
  const formattedTime = now.toLocaleString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });

  // Theme-based styling
  const isDark = resolvedTheme === 'dark';
  const cardBg = isDark ? 'bg-black' : 'bg-white';
  const textColor = isDark ? 'text-white' : 'text-black';
  const mutedTextColor = isDark ? 'text-gray-500' : 'text-gray-500';
  const borderColor = isDark ? 'border-gray-700' : 'border-gray-200';
  const viewsTextColor = isDark ? 'text-white' : 'text-black';

  return (
    <div className="mx-auto max-w-3xl p-6">
      {/* Post Status Section */}
      <div className="mb-4">
        {isScheduled ? (
          <ScheduledPostActions 
            companyContent={selectedCompanyContent as unknown as CompanyContent} 
            selectedProfile={selectedProfile}
          />
        ) : isPublished ? (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <p className="text-green-800 font-medium">Post is published</p>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  asChild
                >
                  <a 
                    href={
                      ayrshareStatus?.postIds?.[0]?.postUrl || 
                      selectedCompanyContent?.published_url || 
                      '#'
                    } 
                    target="_blank" 
                    rel="noopener noreferrer"
                  >
                    View Post
                  </a>
                </Button>
              </div>
            </div>
            <p className="text-green-700 text-sm mt-1">
              Published posts cannot be edited
            </p>
          </div>
        ) : (
          <div className="text-center flex justify-end w-full">
            <PostDialog selectedProfile={selectedProfile} companyContent={selectedCompanyContent as unknown as CompanyContent} />
          </div>
        )}
      </div>
      
      {/* Twitter post preview card */}
      <Card className={`w-full ${cardBg} ${textColor} shadow-sm border ${borderColor} rounded-xl overflow-hidden`}>
        <div className="p-4">
          {/* Post header with user info */}
          <div className="flex items-start space-x-3 mb-3">
            <Avatar className={`h-12 w-12 rounded-full border ${borderColor}`}>
              {profilePicture ? (
                <img src={profilePicture} alt={name} />
              ) : (
                <div className="h-12 w-12 rounded-full border bg-gray-200 flex items-center justify-center">
                  <UserIcon className="h-6 w-6 text-gray-500" />
                </div>
              )}
            </Avatar>
            
            <div className="flex-1">
              <div className="flex items-center">
                <h3 className="font-bold text-[15px]">{name}</h3>
                <span className={`${mutedTextColor} text-sm ml-2`}>@{screenName}</span>
                <span className={`ml-2 ${mutedTextColor}`}>·</span>
                <span className={`${mutedTextColor} text-sm ml-2`}>Now</span>
              </div>
              
              {/* Post content */}
              <div className="mt-8 mb-1 whitespace-pre-line text-[15px]">
                <GeneralContentEditor 
                  editable={!selectedCompanyContent?.is_published && !selectedCompanyContent?.is_scheduled} 
                  companyContent={selectedCompanyContent as unknown as CompanyContent} 
                />
              </div>

              {/* Post media - either video OR images */}
              {mediaToShow.length > 0 && (
                <div className="mt-3 mb-3">
                  {shouldShowVideo ? (
                    // Show video
                    <div className={`rounded-xl overflow-hidden border ${borderColor} relative group`}>
                      <video 
                        src={mediaToShow[0]} 
                        className="w-full object-cover"
                        controls
                        loop
                        muted
                      />
                      {!selectedCompanyContent?.is_published && !selectedCompanyContent?.is_scheduled && (
                        <Button
                          variant="destructive"
                          size="icon"
                          className="absolute top-2 right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={() => removeImage(mediaToShow[0]!)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  ) : (
                    // Show images
                    <>
                      {imageUrls.length === 1 && (
                        <div className={`rounded-xl overflow-hidden border ${borderColor}`}>
                          <ImageWithRemoveButton
                            src={imageUrls[0]!}
                            alt="Tweet image"
                            className="w-full object-cover"
                            onRemove={() => removeImage(imageUrls[0]!)}
                            showRemoveButton={!selectedCompanyContent?.is_published && !selectedCompanyContent?.is_scheduled}
                            borderColor={borderColor}
                            onClick={() => handleImageClick(imageUrls[0]!)}
                          />
                        </div>
                      )}
                      {imageUrls.length === 2 && (
                        <div className={`grid grid-cols-2 gap-1 rounded-xl overflow-hidden border ${borderColor}`}>
                          {imageUrls.map((url, index) => (
                            <ImageWithRemoveButton
                              key={index}
                              src={url}
                              alt={`Tweet image ${index + 1}`}
                              className="w-full h-[200px] object-cover"
                              onRemove={() => removeImage(url)}
                              showRemoveButton={!selectedCompanyContent?.is_published && !selectedCompanyContent?.is_scheduled}
                              borderColor={borderColor}
                              onClick={() => handleImageClick(url)}
                            />
                          ))}
                        </div>
                      )}
                      {imageUrls.length === 3 && (
                        <div className={`grid grid-cols-2 gap-1 rounded-xl overflow-hidden border ${borderColor}`}>
                          <ImageWithRemoveButton
                            src={imageUrls[0]!}
                            alt="Tweet image 1"
                            className="w-full h-[200px] object-cover row-span-2"
                            onRemove={() => removeImage(imageUrls[0]!)}
                            showRemoveButton={!selectedCompanyContent?.is_published && !selectedCompanyContent?.is_scheduled}
                            borderColor={borderColor}
                            onClick={() => handleImageClick(imageUrls[0]!)}
                          />
                          <div className="grid grid-rows-2 gap-1">
                            <ImageWithRemoveButton
                              src={imageUrls[1]!}
                              alt="Tweet image 2"
                              className="w-full h-[99px] object-cover"
                              onRemove={() => removeImage(imageUrls[1]!)}
                              buttonSize="small"
                              showRemoveButton={!selectedCompanyContent?.is_published && !selectedCompanyContent?.is_scheduled}
                              borderColor={borderColor}
                              onClick={() => handleImageClick(imageUrls[1]!)}
                            />
                            <ImageWithRemoveButton
                              src={imageUrls[2]!}
                              alt="Tweet image 3"
                              className="w-full h-[99px] object-cover"
                              onRemove={() => removeImage(imageUrls[2]!)}
                              buttonSize="small"
                              showRemoveButton={!selectedCompanyContent?.is_published && !selectedCompanyContent?.is_scheduled}
                              borderColor={borderColor}
                              onClick={() => handleImageClick(imageUrls[2]!)}
                            />
                          </div>
                        </div>
                      )}
                      {imageUrls.length === 4 && (
                        <div className={`grid grid-cols-2 gap-1 rounded-xl overflow-hidden border ${borderColor}`}>
                          <ImageWithRemoveButton
                            src={imageUrls[0]!}
                            alt="Tweet image 1"
                            className="w-full h-[200px] object-cover"
                            onRemove={() => removeImage(imageUrls[0]!)}
                            showRemoveButton={!selectedCompanyContent?.is_published && !selectedCompanyContent?.is_scheduled}
                            borderColor={borderColor}
                            onClick={() => handleImageClick(imageUrls[0]!)}
                          />
                          <ImageWithRemoveButton
                            src={imageUrls[1]!}
                            alt="Tweet image 2"
                            className="w-full h-[200px] object-cover"
                            onRemove={() => removeImage(imageUrls[1]!)}
                            showRemoveButton={!selectedCompanyContent?.is_published && !selectedCompanyContent?.is_scheduled}
                            borderColor={borderColor}
                            onClick={() => handleImageClick(imageUrls[1]!)}
                          />
                          <ImageWithRemoveButton
                            src={imageUrls[2]!}
                            alt="Tweet image 3"
                            className="w-full h-[200px] object-cover"
                            onRemove={() => removeImage(imageUrls[2]!)}
                            showRemoveButton={!selectedCompanyContent?.is_published && !selectedCompanyContent?.is_scheduled}
                            borderColor={borderColor}
                            onClick={() => handleImageClick(imageUrls[2]!)}
                          />
                          <ImageWithRemoveButton
                            src={imageUrls[3]!}
                            alt="Tweet image 4"
                            className="w-full h-[200px] object-cover"
                            onRemove={() => removeImage(imageUrls[3]!)}
                            showRemoveButton={!selectedCompanyContent?.is_published && !selectedCompanyContent?.is_scheduled}
                            borderColor={borderColor}
                            onClick={() => handleImageClick(imageUrls[3]!)}
                          />
                        </div>
                      )}
                    </>
                  )}
                </div>
              )}

              {/* Post time */}
              <div className={`${mutedTextColor} text-sm mt-2`}>
                {formattedTime} · {formattedDate} · <span className={`${viewsTextColor} font-bold`}>4,968 </span> Views
              </div>
              
              {/* Engagement stats */}
              <div className={`flex justify-between items-center py-3 border-t ${borderColor} mt-3`}>
                <div className="flex space-x-12">
                  <div className={`flex items-center ${mutedTextColor} hover:text-blue-400`}>
                    <svg viewBox="0 0 24 24" width="18" height="18" fill="currentColor">
                      <path d="M1.751 10c0-4.42 3.584-8 8.005-8h4.366c4.49 0 8.129 3.64 8.129 8.13 0 2.96-1.607 5.68-4.196 7.11l-8.054 4.46v-3.69h-.067c-4.49.1-8.183-3.51-8.183-8.01zm8.005-6c-3.317 0-6.005 2.69-6.005 6 0 3.37 2.77 6.08 6.138 6.01l.351-.01h1.761v2.3l5.087-2.81c1.951-1.08 3.163-3.13 3.163-5.36 0-3.39-2.744-6.13-6.129-6.13H9.756z"></path>
                    </svg>
                    <span className="ml-1">0</span>
                  </div>
                  <div className={`flex items-center ${mutedTextColor} hover:text-green-400`}>
                    <svg viewBox="0 0 24 24" width="18" height="18" fill="currentColor">
                      <path d="M4.5 3.88l4.432 4.14-1.364 1.46L5.5 7.55V16c0 1.1.896 2 2 2H13v2H7.5c-2.209 0-4-1.79-4-4V7.55L1.432 9.48.068 8.02 4.5 3.88zM16.5 6H11V4h5.5c2.209 0 4 1.79 4 4v8.45l2.068-1.93 1.364 1.46-4.432 4.14-4.432-4.14 1.364-1.46 2.068 1.93V8c0-1.1-.896-2-2-2z"></path>
                    </svg>
                    <span className="ml-1">0</span>
                  </div>
                  <div className={`flex items-center ${mutedTextColor} hover:text-red-400`}>
                    <svg viewBox="0 0 24 24" width="18" height="18" fill="currentColor">
                      <path d="M16.697 5.5c-1.222-.06-2.679.51-3.89 2.16l-.805 1.09-.806-1.09C9.984 6.01 8.526 5.44 7.304 5.5c-1.243.07-2.349.78-2.91 1.91-.552 1.12-.633 2.78.479 4.82 1.074 1.97 3.257 4.27 7.129 6.61 3.87-2.34 6.052-4.64 7.126-6.61 1.111-2.04 1.03-3.7.477-4.82-.561-1.13-1.666-1.84-2.908-1.91zm4.187 7.69c-1.351 2.48-4.001 5.12-8.379 7.67l-.503.3-.504-.3c-4.379-2.55-7.029-5.19-8.382-7.67-1.36-2.5-1.41-4.86-.514-6.67.887-1.79 2.647-2.91 4.601-3.01 1.651-.09 3.368.56 4.798 2.01 1.429-1.45 3.146-2.1 4.796-2.01 1.954.1 3.714 1.22 4.601 3.01.896 1.81.846 4.17-.514 6.67z"></path>
                    </svg>
                    <span className="ml-1">0</span>
                  </div>
                  <div className={`flex items-center ${mutedTextColor} hover:text-blue-400`}>
                    <svg viewBox="0 0 24 24" width="18" height="18" fill="currentColor">
                      <path d="M12 2.59l5.7 5.7-1.41 1.42L13 6.41V16h-2V6.41l-3.3 3.3-1.41-1.42L12 2.59zM21 15l-.02 3.51c0 1.38-1.12 2.49-2.5 2.49H5.5C4.11 21 3 19.88 3 18.5V15h2v3.5c0 .28.22.5.5.5h12.98c.28 0 .5-.22.5-.5L19 15h2z"></path>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}
