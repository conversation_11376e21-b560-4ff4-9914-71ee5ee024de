import React, { useState } from 'react';
import { Info, Loader2 } from "lucide-react";
import { Label } from "@kit/ui/label";
import { Tooltip, TooltipContent, TooltipTrigger } from "@kit/ui/tooltip";
import { Textarea } from "@kit/ui/textarea";
import { useImageContent } from '../../ContentStudioContext';
import { useZero } from '~/hooks/use-zero';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import axios from 'axios';
import { toast } from 'sonner';
import { useParams } from 'next/navigation';

export const VisualDescriptionInput: React.FC = () => {
  // Get input/setInput from context
  const { input, setInput } = useImageContent();
  const zero = useZero();
  const [isGeneratingDescription, setIsGeneratingDescription] = useState(false);
  const params = useParams();
  const [companyBrand] = useZeroQuery(
    zero.query.company_brand,
    {
      ttl: '10m'
    }
  );
  
  const [companyContent] = useZeroQuery(
    zero.query.company_content,
    {
      ttl: '10m'
    }
  );
  const filteredCompanyContent = companyContent.filter((content: any) => content.id == params.id)[0] || {};
  console.log('compadfnyBrand', companyBrand);
  console.log('companydfBrand', companyContent);
  const handleGetVisualDescription = async () => {
    try {
      setIsGeneratingDescription(true);
      const visualDescription = await axios.post('/api/ai/generate-visual-description', {
        brand_brief: JSON.stringify(companyBrand)|| "NOT_PROVIDED",
        // @ts-expect-error fix typing error 
        content: filteredCompanyContent.task_description|| "NOT_PROVIDED",
        initial_visual_desc: input|| "NOT_PROVIDED",
        // image_gen_styles: JSON.stringify(imageOptions)|| "NOT_PROVIDED"
      });
      console.log('visualDescription', visualDescription);
      setInput(visualDescription.data);
      setIsGeneratingDescription(false);
    } catch (error) {
      setIsGeneratingDescription(false);
      toast.error('Error generating visual description');
      console.error('Error generating visual description:', error);
    }
  }

  return (
    <>
      <div className="flex mt-10 items-center gap-2">
        <Label htmlFor="visual-description">Visual Description</Label>
        <Tooltip>
          <TooltipTrigger asChild>
            <Info className="h-4 w-4 text-muted-foreground cursor-help" />
          </TooltipTrigger>
          <TooltipContent>
            <p className="max-w-xs">Describe the image you want to generate in detail. The more specific your description, the better the generated image will match your vision.</p>
          </TooltipContent>
        </Tooltip>
      </div>
      <p 
        onClick={() => !isGeneratingDescription && handleGetVisualDescription()} 
        className={`underline text-blue-400 text-xs ${isGeneratingDescription ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'} flex items-center gap-2`}
      >
        {isGeneratingDescription && <Loader2 className="h-3 w-3 animate-spin" />}
        {input ? "Improve Visual Description for me." : "Generate Visual Description for me."}
      </p>
      <Textarea
        id="visual-description"
        placeholder="Describe the image you want to generate..."
        value={input}
        onChange={(e) => setInput(e.target.value)}
        className="min-h-[100px]"
      />
    </>
  );
};