'use client'
import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from "@kit/ui/button";
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useBrandData } from '~/hooks/use-brand-data';
import { extractBrandBrief } from '~/utils/brief.util';
import { ScrollArea } from "@kit/ui/scroll-area";
import { Badge } from "@kit/ui/badge";
import { Label } from "@kit/ui/label";
import { Separator } from "@kit/ui/separator";
import { Textarea } from "@kit/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@kit/ui/tabs";
import { updateCompanyContent } from '~/services/company-content';
import { Loader2 } from "lucide-react";
import { useBaseContent, useEditorContent } from '../../context/ContentStudioContext';
import { SelectedDocument } from '~/components/document-selector';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@kit/ui/accordion";
import { Persona } from '~/types/persona';
import { AdvancedOptions } from './AdvancedOptions';
import { getAIExtension } from "@blocknote/xl-ai";
import { useZero } from '~/hooks/use-zero';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useParams, usePathname } from 'next/navigation';

// import { useRouter } from 'next/navigation';
interface GeneratedContent {
  content: string;
  cta_variations: string[];
  headline: string;
  rationale_for_creative_choices: string;
  seo_keywords_used: string[];
  trend_keywords_used: string[];
}

export const TextContentEditor: React.FC = () => {
  // Get data from context
  const params = useParams();
  const contentId = params.id;
    const { editor } = useEditorContent();
    const zero = useZero();
  
    const [companyContent] = useZeroQuery(
    zero.query.company_content,
    {
      ttl: '1m'
    }
  );

  const selectedCompanyContent = companyContent.filter((content: any) => content.id === contentId)[0];
  
    const [savedResearch] = useZeroQuery(
    zero.query.saved_research,
    {
      ttl: '1m'
    }
  );
   
  const [companyBrand] = useZeroQuery(
    zero.query.company_brand,
    {
      ttl: '1m'
    }
  );

  const [personas] = useZeroQuery(
    zero.query.personas,
    {
      ttl: '1m'
    }
  );

  const [icps] = useZeroQuery(
    zero.query.icps,
    {
      ttl: '1m'
    }
  );

  // Add company_campaigns query
  const [companyCampaigns] = useZeroQuery(
    zero.query.company_campaigns,
    {
      ttl: '1m'
    }
  );

  // Add product_documents query
  const [productDocuments] = useZeroQuery(
    zero.query.product_documents,
    {
      ttl: '1m'
    }
  );

  // Local state

  const [generatedContent, setGeneratedContent] = useState<GeneratedContent | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [taskTitle, setTaskTitle] = useState('');
  const [taskDescription, setTaskDescription] = useState('');
  
  // Advanced options state
  const [trendKeywords, setTrendKeywords] = useState<string[]>([]);
  const [seoKeywords, setSeoKeywords] = useState<string[]>([]);
  const [selectedDocuments, setSelectedDocuments] = useState<SelectedDocument[]>([]);
  const [selectedIcps, setSelectedIcps] = useState<string[]>([]);
  const [icpItems, setIcpItems] = useState<any[]>(icps || []);
  const [selectedPersonas, setSelectedPersonas] = useState<any[]>([]);
  const [personaItems, setPersonaItems] = useState<any[]>(personas || []);
  const [selectedResearch, setSelectedResearch] = useState<string[]>([]);
  const [researchItems, setResearchItems] = useState<any[]>(savedResearch || []);

  // Initialize task title and description from company content
  useEffect(() => {
    if (selectedCompanyContent) {
      setTaskTitle(selectedCompanyContent?.task_title || '');
      setTaskDescription(selectedCompanyContent?.task_description || '');
    }
  }, [selectedCompanyContent]);

  // Pre-populate advanced options from campaign data
  useEffect(() => {

    if (selectedCompanyContent?.campaign_id && companyCampaigns?.length > 0) {
      const associatedCampaign = companyCampaigns.find(
        (campaign: any) => campaign.id === selectedCompanyContent.campaign_id
      );
      console.log('associatedCampaign', associatedCampaign);
      if (associatedCampaign) {
        // Pre-populate target_icps
        if (associatedCampaign.target_icps && Array.isArray(associatedCampaign.target_icps)) {
          setSelectedIcps(associatedCampaign.target_icps);
        }
        
        // Pre-populate target_personas
        if (associatedCampaign.target_personas && Array.isArray(associatedCampaign.target_personas)) {
          setSelectedPersonas(associatedCampaign.target_personas);
        }
        
        // Pre-populate external_research
        if (associatedCampaign.external_research && Array.isArray(associatedCampaign.external_research)) {
          setSelectedResearch(associatedCampaign.external_research);
        }
       
        // Pre-populate documents - convert document IDs to SelectedDocument objects
        if (associatedCampaign.documents && Array.isArray(associatedCampaign.documents) && productDocuments?.length > 0) {
          const selectedDocs: SelectedDocument[] = associatedCampaign.documents
            .map((docId: string) => {
              const doc = productDocuments.find((pd: any) => pd.id === docId);
              return doc ? {
                id: doc.id,
                documentTitle: doc.title,
                content: doc.content || ''
              } : null;
            })
            .filter(Boolean) as SelectedDocument[];
          
          setSelectedDocuments(selectedDocs);
        }
      }
    }
  }, [selectedCompanyContent, companyCampaigns, productDocuments]);


  const generateContent = async () => {
    console.log("Generating content");
    setIsGenerating(true);
    setHasError(false);
    if (!selectedCompanyContent?.id || !editor ) return;
    
    try {
      // Build a structured, high-quality prompt
      const prompt =
      `
       Ignore the previouse instruction "Because the document is empty, first update the empty block before adding new blocks."
        Do not stop until you have completed generating the content you have been asked to generate. Do not do partial creations, only full generations. For example, if you are asked to generate a blog post, create the full blog post. 
        This is the most critical instruction. Do not stop until you have completed generating the content you have been asked to generate. Do not do partial creations, only full generations. For example, if you are asked to generate a blog post, create the full blog post. 
        <CONTEXT_BLOCK>
          <CONTENT_BRIEF>
              <Channel>${selectedCompanyContent?.channel || 'Not specified'}</Channel>
              <ContentType>${selectedCompanyContent?.content_type || 'Not specified'}</ContentType>
              <Topic>${taskDescription}</Topic>
          </CONTENT_BRIEF>

      <AUDIENCE_CONTEXT>
          ${/* Your existing logic for Personas and ICPs, slightly modified */ ''}
          ${selectedPersonas.length > 0 ? `
          <Personas>
              ${personas.filter(p => selectedPersonas.includes(p.id)).map(p => {
                  const personaData = p.data && typeof p.data === 'object' ? p.data : {};
                  return `<Persona name="${p.name}">\n<Description>${JSON.stringify((personaData as any)?.data) || 'Target audience segment'}</Description>\n</Persona>`;
              }).join('\n')}
          </Personas>` : ''}

          ${selectedIcps.length > 0 ? `
          <IdealCustomerProfiles>
              ${icps.filter(i => selectedIcps.includes(i.id)).map(i => {
                  const icpData = i.data && typeof i.data === 'object' ? i.data : {};
                  return `<ICP name="${i.name}">\n<Description>${JSON.stringify((icpData as any).data) || 'Ideal customer profile'}</Description>\n</ICP>`;
              }).join('\n')}
          </IdealCustomerProfiles>` : ''}
      </AUDIENCE_CONTEXT>

      <RESEARCH_MATERIALS>
          ${selectedResearch.length > 0 ? researchItems
              .filter(r => selectedResearch.includes(r.id))
              .map((r, index) => {
                  const researchData = r.data && typeof r.data === 'object' ? r.data : {};
                  return `<research_article_${index + 1}>
                    <title>${r.name}</title>
                    <description>${(researchData as any).description || (researchData as any).summary || 'External research insight'}</description>
                    <full_content>${(researchData as any).source_content || ''}</full_content>
                    </research_article_${index + 1}>`;
                                }).join('\n\n') : '<Message>No third-party research was provided.</Message>'}
      </RESEARCH_MATERIALS>

      <COMPANY_PRODUCT_KNOWLEDGE_BASE>
          ${/* Your existing document logic */ ''}
          ${selectedDocuments.length > 0 ? selectedDocuments
              .map(doc => `<Document title="${doc.documentTitle}">\n${doc.content.substring(0, 1000)}${doc.content.length > 1000 ? '...' : ''}\n</Document>`)
              .join('\n\n') : '<Message>No company documents were provided.</Message>'}
      </COMPANY_PRODUCT_KNOWLEDGE_BASE>

      <KEYWORD_STRATEGY>
          <SEO_Keywords>${seoKeywords.join(', ')}</SEO_Keywords>
          <Trending_Keywords>${trendKeywords.join(', ')}</Trending_Keywords>
      </KEYWORD_STRATEGY>

      <BRAND_GUIDELINES>
         ${JSON.stringify(companyBrand)}
      </BRAND_GUIDELINES>
  </CONTEXT_BLOCK>
  You are "Cognitive Creator," an expert AI copywriter and content strategist. Your core function is to synthesize brand information, audience data, and research into high-performing content tailored for specific marketing channels. You follow all instructions with precision.

<TASK>
Synthesize all information within the <CONTEXT_BLOCK> to create engaging content.

**PRIMARY DIRECTIVES:**
1.  **Adhere to Brand:** The <BRAND_GUIDELINES> are the highest priority. The specified <Voice> and <Personality> must be perfectly reflected in the output. This is non-negotiable.
2.  **Target the Audience:** Tailor the language, examples, and tone specifically to the <AUDIENCE_CONTEXT>. Address their needs and pain points directly.
3.  **Position as the Solution:** Use the <RESEARCH_MATERIALS> for context, stats, and credibility. ALWAYS position the company/product from the <COMPANY_PRODUCT_KNOWLEDGE_BASE> as the primary solution to problems identified in the research. Never promote third parties.
4.  **Incorporate Keywords:** Naturally weave terms from the <KEYWORD_STRATEGY> into the content.
5.  **Be Factual:** Ensure any product or company claims are supported by the <COMPANY_PRODUCT_KNOWLEDGE_BASE>. Do not invent features or facts.
6.  **Do Not Invent Social Proof:** Do not create fake customer names or quotes. You can suggest a placeholder like "[Insert customer testimonial here]" if appropriate for the content type.


**CHANNEL-SPECIFIC RULES:**
Based on the <Channel> specified in the <CONTENT_BRIEF>, you must follow these structural rules:

*   **If Channel is "LinkedIn Post":**
    *   **Structure:** Start with a strong hook. Use short paragraphs (1-2 sentences). Use bullet points or numbered lists for readability. End with a question to drive engagement.
    *   **Length:** 150-250 words.
    *   **Tone:** Professional, insightful, and value-driven.
    *   **Hashtags:** Provide 3-5 relevant, professional hashtags.

*   **If Channel is "Tweet" or "X Post":**
    *   **Structure:** A short, punchy, and engaging message.
    *   **Length:** Strictly under 280 characters.
    *   **Tone:** Conversational and concise. Emojis are acceptable if they match the brand personality.
    *   **Hashtags:** Provide 1-3 highly relevant hashtags.

*   **If Channel is "Blog Post" or "Article":**
    *   **Structure:** Create a compelling H1 title. Write a short introduction. Structure the main content with 3-4 H2 subheadings. Conclude with a summary and a strong call_to_action.
    *   **Length:** 600-1000 words.
    *   **Tone:** Informative, in-depth, and authoritative, aligned with the brand voice.
    *   **SEO:** Suggest 5-7 relevant meta tags in the output.

*   **If Channel is "Facebook Ad":**
    *   **Structure:** Provide three distinct components: a short, attention-grabbing headline, persuasive primary_text focusing on benefits, and a direct call_to_action_text.
    *   **Tone:** Persuasive, clear, and benefit-driven.

*   **If Channel is not specified or doesn't match above:**
    *   **Structure:** Create a general-purpose piece of content with a clear beginning, middle, and end.
    *   **Tone:** Follow the brand voice.
    *   **Action:** End with a clear call-to-action.


</TASK>
      `

      // // Call BlockNote's AI extension
      await getAIExtension(editor).callLLM({
        userPrompt: prompt,
        useSelection: false, // Generate new content rather than modifying selection
        defaultStreamTools: {
          add: true,    // Allow adding new blocks
          update: true, // Allow updating existing blocks
          delete: true // Allow deleting blocks
        },
        deleteEmptyCursorBlock: true, // Remove empty blocks when starting to write
      });
      
      // Set generating to false when successfully completed
      setIsGenerating(false);
    } catch (error) {
      console.error("Error generating content:", error);
      setHasError(true);
      setIsGenerating(false);
    }
  }

  const onDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      setTaskDescription(e.target.value);
      zero.mutate.company_content.update({
        id: selectedCompanyContent?.id || '',
        values: {
          task_description: e.target.value,
        }
      });
  }

  return (
    <div className="space-y-4 p-4">
      {/* <div className="space-y-2">
        <Label className="text-lg font-semibold">Task Title</Label>
        <Textarea 
          value={taskTitle} 
          onChange={(e) => setTaskTitle(e.target.value)}
          className="text-lg"
          rows={2}
          disabled={isGenerating}
        />
      </div> */}

      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="basic">Basic</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>
        
        <TabsContent value="basic" className="space-y-4">
          <div className="space-y-2">
            <Label className="text-lg font-semibold">Topic</Label>
            <br />
            <Label className="text-sm text-muted-foreground">Enter the topic or basis used to generate the content.</Label>
            
            <Textarea 
              value={taskDescription} 
              onChange={onDescriptionChange}
              className="text-muted-foreground"
              rows={4}
              disabled={isGenerating}
            />
          </div>
        </TabsContent>
        
        <TabsContent value="advanced" className="space-y-4">
          <div className="space-y-2">
            <Label className="text-lg font-semibold">Topic</Label>
            <br />
            <Label className="text-sm text-muted-foreground">Enter the topic or basis used to generate the content.</Label>
            <Textarea 
              value={taskDescription}   
              onChange={onDescriptionChange}
              className="text-muted-foreground"
              rows={4}
              disabled={isGenerating}
            />
          </div>
          <AdvancedOptions
            selectedDocuments={selectedDocuments}
            onDocumentsChange={setSelectedDocuments}
            selectedIcps={selectedIcps}
            onIcpsChange={setSelectedIcps}
            icps={icpItems}
            onIcpsListChange={setIcpItems}
            selectedPersonas={selectedPersonas}
            onPersonasChange={setSelectedPersonas}
            personas={personaItems}
            onPersonasListChange={setPersonaItems}
            trendKeywords={trendKeywords}
            onTrendKeywordsChange={setTrendKeywords}
            seoKeywords={seoKeywords}
            onSeoKeywordsChange={setSeoKeywords}
            selectedResearch={selectedResearch}
            onResearchChange={setSelectedResearch}
            researchItems={researchItems}
            onResearchItemsChange={setResearchItems}
          />
        </TabsContent>
      </Tabs>
      
      <Button 
        onClick={generateContent}
        disabled={isGenerating || !taskTitle.trim() || !taskDescription.trim()}
      >
        {isGenerating ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Generating...
          </>
        ) : (
          'Generate'
        )}
      </Button>

      {hasError && (
        <div className="mt-2">
          <button
            onClick={generateContent}
            className="text-sm text-red-600 hover:text-red-800 underline cursor-pointer"
          >
            Error Generating, Click to try again
          </button>
        </div>
      )}

      {/* {generatedContent && (
        <ScrollArea className="h-[calc(100vh-200px)]">
          <div className="space-y-6">
            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="keywords">
                <AccordionTrigger className="px-4 py-2 hover:no-underline">
                  <Label className="text-lg font-semibold">Keywords</Label>
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4">
                  <div className="space-y-4">
                    <div>
                      <Label className="text-sm">SEO Keywords</Label>
                      <div className="mt-2 flex flex-wrap gap-2">
                        {generatedContent.seo_keywords_used.map((keyword, index) => (
                          <Badge key={index} variant="secondary">{keyword}</Badge>
                        ))}
                      </div>
                    </div>
                    {generatedContent.trend_keywords_used.length > 0 && (
                      <div>
                        <Separator className="my-4" />
                        <Label className="text-sm">Trend Keywords</Label>
                        <div className="mt-2 flex flex-wrap gap-2">
                          {generatedContent.trend_keywords_used.map((keyword, index) => (
                            <Badge key={index} variant="outline">{keyword}</Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem value="rationale">
                <AccordionTrigger className="px-4 py-2 hover:no-underline">
                  <Label className="text-lg font-semibold">Creative Rationale</Label>
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4">
                  <p>{generatedContent.rationale_for_creative_choices}</p>
                </AccordionContent>
              </AccordionItem>
            </Accordion>

          </div>
        </ScrollArea>
      )} */}
    </div>
  );
};