import React from 'react';
import { Video, Edit } from 'lucide-react';
import { useVideoContent } from '../../ContentStudioContext';
import { Button } from "@kit/ui/button";

export const VideoSubmenu: React.FC = () => {
  const { setSelectedVideoOption } = useVideoContent();

  const options = [
    {
      id: 'existing',
      title: 'Use Existing Video',
      description: 'Select from your uploaded videos',
      icon: <Video className="h-5 w-5 text-blue-600 dark:text-blue-300" />,
      bgColor: 'bg-blue-100 dark:bg-blue-900'
    },
    // {
    //   id: 'create',
    //   title: 'Video Studio',
    //   description: 'Create, edit, or generate professional videos with our editor',
    //   icon: <Edit className="h-5 w-5 text-purple-600 dark:text-purple-300" />,
    //   bgColor: 'bg-purple-100 dark:bg-purple-900'
    // },
    // {
    //   id: 'generate',
    //   title: 'Generate Video with AI',
    //   description: 'Create professional videos using AI. Describe your vision and let our AI bring it to life.',
    //   icon: <Sparkles className="h-5 w-5 text-indigo-600 dark:text-indigo-300" />,
    //   bgColor: 'bg-indigo-100 dark:bg-indigo-900'
    // }
  ];

  return (
    <div className="space-y-4">
      {options.map((option) => (
        <div
          key={option.id}
          className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow"
        >
          <div className="flex items-center gap-3 mb-4">
            <div className={`${option.bgColor} p-2 rounded-full`}>
              {option.icon}
            </div>
            <h3 className="font-medium text-lg">{option.title}</h3>
          </div>
          <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
            {option.description}
          </p>
          <Button 
            onClick={() => setSelectedVideoOption(option.id as any)}
            className="w-full mt-2"
          >
            <span className="flex items-center">
              {option.icon}
              <span className="ml-2">{option.title}</span>
            </span>
          </Button>
        </div>
      ))}
    </div>
  );
}; 