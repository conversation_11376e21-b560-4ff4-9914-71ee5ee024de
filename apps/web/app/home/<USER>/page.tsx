'use client';
import { PageBody } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

import { TeamAccountLayoutPageHeader } from './_components/layout';
import { FourQuadrantLayout } from './_components/dashboard-components/four-quadrant-layout';
import { OnboardingChecklist } from './_components/dashboard-components/onboarding-checklist';

function TeamAccountHomePage() {
  const workspace = useTeamAccountWorkspace();

  return (
    <>
      <TeamAccountLayoutPageHeader
        account={workspace.account.id}
        title={<Trans i18nKey={'common:routes.create'} />}
        description={'Create'}
      />

      <PageBody>
        <FourQuadrantLayout
          topLeft={<OnboardingChecklist />}
          topRight={
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">
                <Trans i18nKey={'create:topRight.title'} defaults="Quick Actions" />
              </h3>
              <p className="text-sm text-muted-foreground">
                <Trans i18nKey={'create:topRight.description'} defaults="Coming soon - quick action shortcuts" />
              </p>
            </div>
          }
          bottomLeft={
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">
                <Trans i18nKey={'create:bottomLeft.title'} defaults="Recent Activity" />
              </h3>
              <p className="text-sm text-muted-foreground">
                <Trans i18nKey={'create:bottomLeft.description'} defaults="Coming soon - recent activity feed" />
              </p>
            </div>
          }
          bottomRight={
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">
                <Trans i18nKey={'create:bottomRight.title'} defaults="Analytics" />
              </h3>
              <p className="text-sm text-muted-foreground">
                <Trans i18nKey={'create:bottomRight.description'} defaults="Coming soon - key metrics and insights" />
              </p>
            </div>
          }
        />
      </PageBody>
    </>
  );
}

export default TeamAccountHomePage;
