'use client';

import { useRouter } from 'next/navigation';
import { useZero } from '~/hooks/use-zero';
import { useTrialStatus } from '~/hooks/use-trial-status';
import { TrialStatusCard } from '~/components/trial-status-card';

interface TrialBillingSectionProps {
  accountId: string;
  accountSlug: string;
}

export function TrialBillingSection({ accountId, accountSlug }: TrialBillingSectionProps) {
  const router = useRouter();
  const zero = useZero();
  const { trialInfo, isLoading } = useTrialStatus({ accountId });

  const handleStartTrial = async () => {
    try {
      await zero.mutate.accounts.startTrial({
        accountId,
        trialDays: 7,
      });
    } catch (error) {
      console.error('Failed to start trial:', error);
    }
  };

  const handleUpgrade = () => {
    // Scroll to checkout section or redirect to upgrade flow
    const checkoutElement = document.getElementById('checkout-section');
    if (checkoutElement) {
      checkoutElement.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleConvertTrial = async () => {
    try {
      await zero.mutate.accounts.convertTrial({ accountId });
    } catch (error) {
      console.error('Failed to convert trial:', error);
    }
  };

  if (isLoading || !trialInfo) {
    return null;
  }

  // Only show trial card for accounts that have trial functionality
  if (trialInfo.status === 'converted') {
    return null; // Don't show for already converted accounts
  }

  return (
    <div className="mb-6">
      <TrialStatusCard
        trialInfo={trialInfo}
        onUpgrade={handleUpgrade}
        onStartTrial={trialInfo.status === 'inactive' ? handleStartTrial : undefined}
      />
    </div>
  );
}
