'use client';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useEffect, useState } from 'react';
import { toast } from '@kit/ui/sonner';
import { useRouter } from 'next/navigation';
import { Trans } from '@kit/ui/trans';
import { Loader2 } from 'lucide-react';

// Import extracted components
import { DashboardHeader } from './dashboard-header';
import { PostTemplatesSection } from './post-templates-section';
import {
  CampaignTemplatesSection,
  CampaignCreationModal,
  type CampaignTemplate,
  type CampaignFormData,
  type PostTemplate,
  getTodayDate,
  calculateEndDate,
  generateCampaignId
} from '../../campaign';


export const Dashboard = () => {
  const router = useRouter();
  const zero = useZero();
  const workspace = useTeamAccountWorkspace();
  const [ filteredDocuments, setFilteredDocuments ] = useState<any[]>([]);
  const [isClient, setIsClient] = useState(false);

    const [campaignTemplates] = useZeroQuery(
      zero.query.campaign_templates,
      {
        ttl: '10m'
      }
    );

  console.log("campaignTemplates", campaignTemplates);
  const [postTemplates] = useZeroQuery(
    zero.query.post_templates,
    {
      ttl: '10m'
    }
  );
  console.log("postTemplates", postTemplates);
  const [savedResearch] = useZeroQuery(
    zero.query.saved_research,
    {
      ttl: '10m'
    }
  );

  console.log("savedResearch", savedResearch);
 
  const [icps] = useZeroQuery(
    zero.query.icps,
    {
      ttl: '10m'
    }
  );
  const [personas] = useZeroQuery(
    zero.query.personas,
    {
      ttl: '10m'
    }
  );


  const [documents] = useZeroQuery(
    zero.query.product_documents
  )

  useEffect(() => {
    if(documents)  setFilteredDocuments(documents);
  }, [documents]);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<CampaignTemplate | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [formData, setFormData] = useState<CampaignFormData>({
    name: '',
    objective: '',
    startDate: getTodayDate(),
    externalResearch: [],
    documents: [],
    icps: [],
    personas: [],
    templateId: ''
  });

  const handleQuickStartCampaign = (template: CampaignTemplate) => {
    setSelectedTemplate(template);
    setFormData({
      name: template.title,
      objective: String(template.goal ?? ''),
      startDate: getTodayDate(),
      externalResearch: [],
      documents: [],
      icps: [],
      personas: [],
      templateId: template.id
    });
    setShowAdvanced(false);
    setIsModalOpen(true);
  };

  const handleFormChange = (field: keyof CampaignFormData, value: string | string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleResearchToggle = (researchId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      externalResearch: checked
        ? [...prev.externalResearch, researchId]
        : prev.externalResearch.filter(id => id !== researchId)
    }));
  };

  const handleProductDocumentToggle = (documentId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      documents: checked
        ? [...prev.documents, documentId]
        : prev.documents.filter(id => id !== documentId)
    }));
  };

  const handleIcpToggle = (icpId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      icps: checked
        ? [...prev.icps, icpId]
        : prev.icps.filter(id => id !== icpId)
    }));
  };

  const handlePersonaToggle = (personaId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      personas: checked
        ? [...prev.personas, personaId]
        : prev.personas.filter(id => id !== personaId)
    }));
  };

  // Post handlers
  const handleQuickStartPost = (template: PostTemplate) => {
    const postId = crypto.randomUUID();
    zero.mutate.company_content.insert({
      id: postId,
      values: { 
        company_id: workspace.account.id,
        content_type: template.content_type,
        channel: template.channel,
        created_at: Date.now(),
        updated_at: Date.now(),
        status: 'draft',
        task_title: "Untitled",
      }
    });
    router.push(`/home/<USER>/studio/${postId}`);
  };

  const handleCreateContentSchedule = async () => {
    if (!selectedTemplate || !workspace.user?.id) {
      toast.error('Missing required information');
      return;
    }

    setIsCreating(true);
    
    try {
      const endDate = calculateEndDate(formData.startDate, selectedTemplate.duration_weeks || 3);
      const id = generateCampaignId();
      console.log("formData", {
        external_research: formData.externalResearch,
        documents: formData.documents,
        target_icps: formData.icps,
        target_personas: formData.personas,
      });
      // Create the campaign using the mutator (user cache will be updated by server mutator)
      zero.mutate.company_campaigns.insert({
        id,
        company_id: workspace.account.id,
        user_id: workspace.user.id!,
        name: formData.name,
        objective: formData.objective,
        start_date: formData.startDate,
        end_date: endDate,
        templateId: formData.templateId,
        external_research: formData.externalResearch,
        documents: formData.documents,
        target_icps: formData.icps,
        target_personas: formData.personas,
      });

      zero.mutate.user_cache.upsert({
        user_id: workspace.user.id!,
        values: {
          selected_campaign: id,
        }
      });
      setIsModalOpen(false)
      toast.success('Campaign created successfully! Content is being generated. Redirecting to tasks...');
      router.push(`/home/<USER>/tasks`);
       //wait for 2 seconds
      //  setTimeout(() => {
      //   setIsModalOpen(false);
      //   router.push(`/home/<USER>/tasks`);
      // }, 2000);
      // Reset form
      setFormData({
        name: '',
        objective: '',
        startDate: getTodayDate(),
        externalResearch: [],
        documents: [],
        icps: [],
        personas: [],
        templateId: ''
      });
      setSelectedTemplate(null);
      setShowAdvanced(false);
      
    } catch (error) {
      console.error('Failed to create campaign:', error);
      toast.error('Failed to create campaign. Please try again.');
    } finally {
      setIsCreating(false);
    }
  };

  // const handleViewAllCampaigns = () => {
  //   // TODO: Navigate to campaigns page
  //   console.log('Navigating to campaigns page');
  // };

  // Show loading state on server and until client hydrates
  if (!isClient || !campaignTemplates || campaignTemplates.length === 0) {
    return (
      <div className="p-6 max-w-7xl mx-auto">
        <DashboardHeader />

        <div className="mb-8 flex flex-col gap-2 justify-center items-center">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-muted-foreground">
                <Trans i18nKey="dashboard:loadingTemplates" defaults="Loading campaign templates..." />
              </p>
            </div>
          </div>
        </div>

        <PostTemplatesSection
          postTemplates={[]}
          onQuickStartPost={handleQuickStartPost}
        />
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <DashboardHeader />

      <CampaignTemplatesSection
        campaignTemplates={campaignTemplates}
        onQuickStartCampaign={handleQuickStartCampaign}
      />

      <PostTemplatesSection
        postTemplates={postTemplates || []}
        onQuickStartPost={handleQuickStartPost}
      />

      <CampaignCreationModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        formData={formData}
        onFormChange={handleFormChange}
        showAdvanced={showAdvanced}
        onToggleAdvanced={() => setShowAdvanced(!showAdvanced)}
        savedResearch={savedResearch || []}
        documents={filteredDocuments || []}
        icps={icps || []}
        personas={personas || []}
        onResearchToggle={handleResearchToggle}
        onDocumentToggle={handleProductDocumentToggle}
        onIcpToggle={handleIcpToggle}
        onPersonaToggle={handlePersonaToggle}
        onCreateCampaign={handleCreateContentSchedule}
        isCreating={isCreating}
      />



    </div>
  );
};