'use client'
import type { User } from '@supabase/supabase-js';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  useSidebar,
} from '@kit/ui/shadcn-sidebar';
import { Button } from '@kit/ui/button';
import { PlusIcon } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';
import { cn } from '@kit/ui/utils';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Checkbox } from '@kit/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Spinner } from '@kit/ui/spinner';
import { createCampaign, getCampaigns } from '~/services/campaign';
import { createCampaignIdea } from '~/services/campaign-idea';
import { createCompanyContent } from '~/services/company-content';
import { Campaign } from '~/types/Campaign';

import { ProfileAccountDropdownContainer } from '~/components/personal-account-dropdown-container';
import { getTeamAccountSidebarConfig } from '~/config/team-account-navigation.config';
import { TeamAccountNotifications } from '../../navigation';
import { TeamAccountAccountsSelector } from '../../navigation';
import { TeamAccountLayoutSidebarNavigation } from './team-account-layout-sidebar-navigation';
import { useBrandData } from '~/hooks/use-brand-data';
import { extractBrandBrief, extractCampaignBrief } from '~/utils/brief.util';
import { useTrialStatus } from '~/hooks/use-trial-status';
import { CompactTrialBadge } from '~/components/trial-badge';

type AccountModel = {
  label: string | null;
  value: string | null;
  image: string | null;
};

export function TeamAccountLayoutSidebar(props: {
  account: string;
  accountId: string;
  accounts: AccountModel[];
  user: User;
}) {
  return (
    <SidebarContainer
      account={props.account}
      accountId={props.accountId}
      accounts={props.accounts}
      user={props.user}
    />
  );
}

function SidebarContainer(props: {
  account: string;
  accountId: string;
  accounts: AccountModel[];
  user: User;
}) {
  const { account, accounts, user } = props;
  const userId = user.id;
  const router = useRouter();
  const config = getTeamAccountSidebarConfig(account);
  const collapsible = config.sidebarCollapsedStyle;
  const [isOpen, setIsOpen] = useState(false);
  const { open } = useSidebar();

  // Get trial status for the current account
  const { trialInfo } = useTrialStatus({ accountId: props.accountId });
  
  // New state for form
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [objective, setObjective] = useState('');
  const [selectedCampaign, setSelectedCampaign] = useState<string>('');
  const [notPartOfCampaign, setNotPartOfCampaign] = useState(false);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const brandData = useBrandData(props.accountId);
  // Fetch campaigns when dialog opens
  useEffect(() => {
    if (isOpen) {
      const fetchCampaigns = async () => {
        try {
          const campaignsData = await getCampaigns(props.accountId);
          setCampaigns(campaignsData);
        } catch (error) {
          console.error('Error fetching campaigns:', error);
        }
      };
      fetchCampaigns();
    }
  }, [isOpen, props.accountId]);

  const handleCreatePost = async () => {
    if (!objective) {
      return;
    }

    setIsLoading(true);
    try {
      let campaignId = selectedCampaign;
      let campaignItem;

      // If no campaign is selected or "not part of campaign" is checked, create a new campaign
      if (notPartOfCampaign || !selectedCampaign) {
        const newCampaign = await createCampaign({
          company_id: props.accountId,
          name: objective,
          status: 'Draft',
          user_id: userId,
        });
        campaignId = newCampaign.id;
        campaignItem = newCampaign;
      } else {
        campaignItem = selectedCampaign;
      }

      //create creative brief
      const response = await fetch('/api/ai/brief', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          brand_name: account,
          brand_brief: brandData.data ? extractBrandBrief(brandData.data) : 'No Brand Brief Provided',
          campaign_brief: extractCampaignBrief(campaignItem),
          //@ts-expect-error maps should work TODO: fix this
          product_info: campaigns.documents ? campaigns.documents?.map((doc: any) => `${doc.title}: ${doc.content}`).join('\n\n') : "No Product Info Provided",
          idea: objective,
          languages: ['en'], // Always set, defaults to 'en'
          supported_content_types: ['LinkedIn Post'],
          supported_channels: ['LinkedIn'],
        }),
      });
      const responseData = await response.json();
      const brief = responseData.creative_brief;

      // Create campaign idea
      const campaignIdea = await createCampaignIdea({
        campaign_id: campaignId,
        company_id: props.accountId,
        content: objective,
        languages: ['en'],
        brief,
      });

      // Create company content
      const companyContent = await createCompanyContent({
        company_id: props.accountId,
        campaign_id: campaignId,
        idea_id: campaignIdea.id,
        content_type: 'LinkedIn Post',
        language: 'en',
        content: objective,
        task_title: objective,
      });
      console.log("companyContent", companyContent);
      // Redirect to the post creation page
      const queryParams = new URLSearchParams();
      queryParams.set('objective', objective);
      queryParams.set('campaignId', campaignId);
      queryParams.set('ideaId', campaignIdea.id);
      queryParams.set('companyContentId', companyContent.id);

      router.push(`/home/<USER>/studio/${companyContent.id}?${queryParams.toString()}`);
    } catch (error) {
      console.error('Error creating post:', error);
    } finally {
      setIsLoading(false);
      setIsOpen(false);
    }
  };

  return (
    <Sidebar collapsible={collapsible}>
      <SidebarHeader className={'h-16 justify-center'}>
        <div className={'flex flex-row items-center justify-between gap-x-3'}>
          <div className={'flex flex-col gap-1'}>
            <TeamAccountAccountsSelector
              userId={userId}
              selectedAccount={account}
              accounts={accounts}
            />
            {trialInfo && (
              <div className={'group-data-[minimized=true]:hidden'}>
                <CompactTrialBadge trialInfo={trialInfo} />
              </div>
            )}
          </div>

          <div className={'group-data-[minimized=true]:hidden'}>
            <TeamAccountNotifications
              userId={userId}
              accountId={props.accountId}
            />
          </div>
        </div>
      </SidebarHeader>
     
      <SidebarContent className={`mt-5 h-[calc(100%-160px)] overflow-y-auto`}>
        <Button
          onClick={() => router.push(`/home/<USER>/create`)}
          variant="outline"
          size={open ? "default" : "icon"}
          className={open ? 'px-4 mx-4' : 'w-9 h-9 mx-auto p-0'}
        >
          <PlusIcon className="h-4 w-4" />
          <span className="ml-2">Create</span>
        </Button>
        <TeamAccountLayoutSidebarNavigation config={config} />
      </SidebarContent>

      <SidebarFooter>
        <SidebarContent>
          <ProfileAccountDropdownContainer user={props.user} />
        </SidebarContent>
      </SidebarFooter>
    </Sidebar>
  );
}
