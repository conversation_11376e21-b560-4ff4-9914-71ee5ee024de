'use client';

import { useState } from 'react';

import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';

import { DataTable } from '@kit/ui/enhanced-data-table';

import { ProductDocument } from '~/types/product-document';

import { PDFViewerModal } from './PDFViewerModal';

interface ProductDocumentsTableProps {
  data: ProductDocument[];
  page: number;
  pageCount: number;
  pageSize: number;
  url?: string;
}

export function ProductDocumentsTable({
  data,
  page,
  pageSize,
  pageCount,
}: ProductDocumentsTableProps) {
  const [selectedFile, setSelectedFile] = useState<ProductDocument | null>(
    null,
  );
  const columns: ColumnDef<ProductDocument>[] = [
    {
      id: 'title',
      header: 'Name',
      cell: ({ row }) => {
        return (
          <p
            className="h-auto cursor-pointer p-0 font-medium hover:text-blue-700"
            onClick={() => setSelectedFile(row.original)}
          >
            {row.original.title}
          </p>
        );
      },
    },
    {
      id: 'createdAt',
      header: 'Created At',
      cell: ({ row }) => {
        return format(new Date(row.original.created_at || ''), 'PPp');
      },
    },
  ];
  console.log({selectedFile});

  return (
    <>
      <DataTable
        data={data}
        columns={columns}
        pageIndex={page - 1}
        pageCount={pageCount}
        pageSize={pageSize}
      />
      <PDFViewerModal
        isOpen={!!selectedFile}
        onClose={() => setSelectedFile(null)}
        fileUrl={selectedFile?.file_path ?? ''} // Use url instead of path
        fileName={selectedFile?.title ?? ''}
        content={selectedFile?.content ?? ''}
        fileType={selectedFile?.file_type ?? ''}
      />
    </>
  );
}
