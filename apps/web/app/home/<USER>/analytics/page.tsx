'use client';

import { <PERSON>Body } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';
import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { AlertCircle } from 'lucide-react';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import { AnalyticsHeader } from './_components/analytics-header';
import { AnalyticsClientWrapper } from './_components/analytics-client-wrapper';

import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useState, useEffect, useMemo } from 'react';

function AnalyticsPage() {
  const zero = useZero();
  const workspace = useTeamAccountWorkspace();
  const [analyticsData, setAnalyticsData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 🚀 Use Zero relationships for automatic joins - much cleaner!
  const [linkedInSocialProfiles] = useZeroQuery(
    zero.query.ayrshare_social_profiles
      .where('company_id', workspace.account.id)
      .where('platform', 'linkedin')
      .where('is_connected', true)
      .related('userProfile'), // 🎉 Auto-join with user profile data!
    {
      ttl: '10m'
    }
  );

  // Transform the data with automatic joins - no manual mapping needed!
  const linkedInProfiles = useMemo(() => {
    if (!linkedInSocialProfiles) return [];
    
    return linkedInSocialProfiles
      .filter(socialProfile => socialProfile.userProfile?.profileKey) // Filter out profiles without profileKey
      .map(socialProfile => ({
        id: socialProfile.id,
        title: socialProfile.display_name || `LinkedIn Profile`,
        profileKey: socialProfile.userProfile!.profileKey, // Now available via relationship!
        user_id: socialProfile.user_id || '',
        company_id: socialProfile.company_id || '',
        created_at: new Date(socialProfile.created_at || Date.now()).toISOString(),
        profile_name: socialProfile.display_name || socialProfile.username || `LinkedIn Profile`,
        userProfileId: socialProfile.userProfile!.id // Direct access via relationship!
      }));
  }, [linkedInSocialProfiles]);

  console.log('LinkedIn profiles found:', linkedInProfiles);

  // Fetch analytics data for the first LinkedIn profile
  useEffect(() => {
    const fetchAnalyticsData = async () => {
      if (!linkedInProfiles.length) return;
      
      const firstProfile = linkedInProfiles[0];
      console.log('🚀 First profile:', firstProfile);
      if (!firstProfile?.profileKey) return;
      
      setLoading(true);
      setError(null);
      
      try {
        console.log('Fetching analytics for LinkedIn profile:', firstProfile);
        const response = await fetch(`/api/analytics/linkedin?profileKey=${encodeURIComponent(firstProfile.profileKey)}`);
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch analytics data');
        }
        
        const data = await response.json();
        console.log("LinkedIn analytics data:", data);
        setAnalyticsData(data);
      } catch (err) {
        console.error('Error fetching LinkedIn analytics:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch analytics data');
      } finally {
        setLoading(false);
      }
    };
    
    fetchAnalyticsData();
  }, [linkedInProfiles]);
  
  // If no LinkedIn profiles are available, show appropriate message
  if (linkedInProfiles.length === 0 && !loading) {
    return (
      <>
        <AnalyticsHeader
          title={<Trans i18nKey="analytics:pageTitle" defaults="LinkedIn Analytics" />}
          description={<Trans i18nKey="analytics:pageDescription" defaults="View your LinkedIn post performance and engagement metrics" />}
        />
        
        <PageBody>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>
              <Trans i18nKey="analytics:noLinkedInProfilesTitle" defaults="No LinkedIn Profiles Found" />
            </AlertTitle>
            <AlertDescription>
              <Trans i18nKey="analytics:noLinkedInProfilesDescription" defaults="You need to connect a LinkedIn profile to view analytics. Please visit the integrations page to connect your LinkedIn account." />
            </AlertDescription>
          </Alert>
        </PageBody>
      </>
    );
  }

  // Show loading state while fetching data
  if (loading && !analyticsData) {
    return (
      <>
        <AnalyticsHeader
          title={<Trans i18nKey="analytics:pageTitle" defaults="LinkedIn Analytics" />}
          description={<Trans i18nKey="analytics:pageDescription" defaults="View your LinkedIn post performance and engagement metrics" />}
        />
        
        <PageBody>
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            <span className="ml-2 text-muted-foreground">
              Loading analytics...
            </span>
          </div>
        </PageBody>
      </>
    );
  }

  // Show error state if there's an error
  if (error) {
    return (
      <>
        <AnalyticsHeader
          title={<Trans i18nKey="analytics:pageTitle" defaults="LinkedIn Analytics" />}
          description={<Trans i18nKey="analytics:pageDescription" defaults="View your LinkedIn post performance and engagement metrics" />}
        />
        
        <PageBody>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>
              <Trans i18nKey="analytics:errorTitle" defaults="Error loading analytics" />
            </AlertTitle>
            <AlertDescription>
              {error}
            </AlertDescription>
          </Alert>
        </PageBody>
      </>
    );
  }

  // Only render the wrapper if we have analytics data
  if (!analyticsData) {
    return (
      <>
        <AnalyticsHeader
          title={<Trans i18nKey="analytics:pageTitle" defaults="LinkedIn Analytics" />}
          description={<Trans i18nKey="analytics:pageDescription" defaults="View your LinkedIn post performance and engagement metrics" />}
        />
        
        <PageBody>
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </PageBody>
      </>
    );
  }

  return (
    <AnalyticsClientWrapper
      profiles={linkedInProfiles}
      initialAnalyticsData={analyticsData}
      initialProfileKey={linkedInProfiles[0]?.profileKey || ''}
    />
  );
}

export default AnalyticsPage;