'use client';

import { useEffect, useState } from 'react';
import { Trans } from '@kit/ui/trans';
import { useTranslation } from 'react-i18next';
import { useTrialGuard } from '../hooks/use-trial-guard';
import { TrialExpirationModal } from './trial-expiration-modal';

interface TrialRouteGuardProps {
  accountId: string;
  children: React.ReactNode;
  requiresPaid?: boolean;
  allowDuringTrial?: boolean;
  redirectOnExpired?: boolean;
  showModal?: boolean;
}

export function TrialRouteGuard({
  accountId,
  children,
  requiresPaid = false,
  allowDuringTrial = true,
  redirectOnExpired = true,
  showModal = true,
}: TrialRouteGuardProps) {
  const { t } = useTranslation('trials');
  const [showExpirationModal, setShowExpirationModal] = useState(false);
  
  const allowedStatuses = (() => {
    if (requiresPaid) {
      return ['converted'] as const;
    }
    if (allowDuringTrial) {
      return ['active', 'converted'] as const;
    }
    return ['converted'] as const;
  })();

  const {
    trialInfo,
    isLoading,
    canAccess,
    shouldShowWarning,
    redirectToUpgrade,
  } = useTrialGuard({
    accountId,
    enabled: true,
    redirectOnExpired: redirectOnExpired && !showModal, // Don't auto-redirect if we're showing modal
    allowedStatuses: [...allowedStatuses],
    onTrialExpired: (trialInfo) => {
      if (showModal && trialInfo.status === 'expired') {
        setShowExpirationModal(true);
      }
    },
    onTrialWarning: (trialInfo) => {
      if (showModal && shouldShowWarning) {
        setShowExpirationModal(true);
      }
    },
  });

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Show access denied state
  if (!canAccess && trialInfo) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4 text-center">
        <div className="text-6xl">🔒</div>
        <h2 className="text-2xl font-semibold">
          <Trans i18nKey="trials:messages.trialAccessDenied" />
        </h2>
        <p className="text-muted-foreground max-w-md">
          {trialInfo.status === 'expired' 
            ? t('messages.trialAccessDeniedMessage')
            : requiresPaid 
            ? t('messages.trialFeatureLockedMessage')
            : t('messages.trialAccessDeniedMessage')
          }
        </p>
        <button
          onClick={redirectToUpgrade}
          className="px-6 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
        >
          {trialInfo.status === 'expired' ? t('actions.upgradeNow') : t('trialDetails')}
        </button>
      </div>
    );
  }

  return (
    <>
      {children}
      
      {/* Trial Expiration Modal */}
      {showModal && trialInfo && (
        <TrialExpirationModal
          trialInfo={trialInfo}
          isOpen={showExpirationModal}
          onClose={() => setShowExpirationModal(false)}
          onUpgrade={() => {
            setShowExpirationModal(false);
            redirectToUpgrade();
          }}
          onDismiss={() => setShowExpirationModal(false)}
          variant={
            trialInfo.status === 'expired' 
              ? 'expired' 
              : shouldShowWarning 
              ? 'warning' 
              : 'reminder'
          }
        />
      )}
    </>
  );
}

interface TrialFeatureGuardProps {
  accountId: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requiresPaid?: boolean;
  allowDuringTrial?: boolean;
  trialDaysRequired?: number;
}

export function TrialFeatureGuard({
  accountId,
  children,
  fallback,
  requiresPaid = false,
  allowDuringTrial = true,
  trialDaysRequired = 0,
}: TrialFeatureGuardProps) {
  const { trialInfo, isLoading } = useTrialGuard({
    accountId,
    enabled: true,
    redirectOnExpired: false,
    allowedStatuses: requiresPaid ? ['converted'] : ['active', 'converted'],
  });

  if (isLoading) {
    return (
      <div className="animate-pulse bg-muted rounded h-8 w-24"></div>
    );
  }

  if (!trialInfo) {
    return <>{children}</>;
  }

  const hasAccess = (() => {
    if (requiresPaid) {
      return trialInfo.isConverted;
    }

    if (allowDuringTrial) {
      if (trialDaysRequired > 0 && trialInfo.daysRemaining !== null) {
        return trialInfo.daysRemaining >= trialDaysRequired || trialInfo.isConverted;
      }
      return trialInfo.isActive || trialInfo.isConverted;
    }

    return trialInfo.isConverted;
  })();

  if (!hasAccess) {
    return fallback || null;
  }

  return <>{children}</>;
}

interface TrialBannerProps {
  accountId: string;
  className?: string;
}

export function TrialBanner({ accountId, className }: TrialBannerProps) {
  const { t } = useTranslation('trials');
  const { trialInfo, shouldShowWarning, redirectToUpgrade } = useTrialGuard({
    accountId,
    enabled: true,
    redirectOnExpired: false,
    allowedStatuses: ['active', 'expired', 'converted'],
  });

  if (!trialInfo || trialInfo.status === 'converted' || trialInfo.status === 'inactive') {
    return null;
  }

  const isUrgent = trialInfo.status === 'expired' || (trialInfo.daysRemaining !== null && trialInfo.daysRemaining <= 1);

  return (
    <div className={`
      ${isUrgent 
        ? 'bg-red-50 border-red-200 text-red-800 dark:bg-red-500/10 dark:border-red-500/20 dark:text-red-400' 
        : 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-500/10 dark:border-blue-500/20 dark:text-blue-400'
      } 
      border-b px-4 py-2 text-sm text-center ${className}
    `}>
      <span className="font-medium">
        {trialInfo.status === 'expired' 
          ? t('messages.trialExpired')
          : trialInfo.daysRemaining !== null && trialInfo.daysRemaining > 0
          ? t('trialExpiresIn', { days: trialInfo.daysRemaining })
          : t('trialActiveDescriptionSoon')
        }
      </span>
      <button
        onClick={redirectToUpgrade}
        className="ml-2 underline hover:no-underline font-medium"
      >
        <Trans i18nKey="trials:actions.upgradeNow" />
      </button>
    </div>
  );
}
