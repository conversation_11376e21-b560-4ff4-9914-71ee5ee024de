'use client';

import { useEffect, useState } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useTrialStatus } from '../hooks/use-trial-status';
import { TrialExpirationModal } from './trial-expiration-modal';

interface GlobalTrialGuardProps {
  accountId: string;
  accountSlug: string;
  children: React.ReactNode;
}

export function GlobalTrialGuard({ accountId, accountSlug, children }: GlobalTrialGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { trialInfo, isLoading } = useTrialStatus({ accountId });
  const [showExpirationModal, setShowExpirationModal] = useState(false);

  // Handle trial expiration with modal-first approach
  useEffect(() => {
    // Don't process while loading
    if (isLoading) return;

    // Don't process if no trial info (personal accounts)
    if (!trialInfo) return;

    // Don't process if already on billing page or auth pages
    if (pathname.includes('/billing') || pathname.includes('/auth')) return;

    // Don't process if trial is active or converted
    if (trialInfo.isActive || trialInfo.isConverted) return;

    // For expired trials, show modal first
    if (trialInfo.isExpired) {
      console.log('🚫 Trial expired - showing modal');
      setShowExpirationModal(true);
      return;
    }

    // Also handle inactive trials (shouldn't happen for team accounts, but safety check)
    if (trialInfo.status === 'inactive') {
      console.log('⚠️ Trial inactive - redirecting to billing page');
      router.replace(`/home/<USER>/billing`);
      return;
    }
  }, [trialInfo, isLoading, pathname, router, accountSlug]);

  // Handle modal upgrade action
  const handleUpgrade = () => {
    setShowExpirationModal(false);
    router.push(`/home/<USER>/billing`);
  };

  // Handle modal close/dismiss
  const handleModalClose = () => {
    setShowExpirationModal(false);
    // After modal is dismissed, redirect to billing page
    setTimeout(() => {
      router.replace(`/home/<USER>/billing`);
    }, 500);
  };

  // Show loading state while checking trial status
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Block access if trial is expired and not on billing page (fallback UI)
  // This should rarely be seen since modals should handle expired trials first
  if (trialInfo?.isExpired && !pathname.includes('/billing')) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen space-y-4 text-center p-8">
        <div className="text-6xl">⏰</div>
        <h1 className="text-2xl font-bold">Trial Expired</h1>
        <p className="text-muted-foreground max-w-md">
          Your 7-day trial has ended. Please upgrade to continue accessing your account.
        </p>
        <button
          onClick={() => router.push(`/home/<USER>/billing`)}
          className="px-6 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
        >
          Upgrade Now
        </button>
      </div>
    );
  }

  return (
    <>
      {children}

      {/* Trial Expiration Modal */}
      {trialInfo && (
        <TrialExpirationModal
          trialInfo={trialInfo}
          isOpen={showExpirationModal}
          onClose={handleModalClose}
          onUpgrade={handleUpgrade}
          onDismiss={handleModalClose}
          variant="expired"
        />
      )}
    </>
  );
}
