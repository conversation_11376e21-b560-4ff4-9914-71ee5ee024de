'use client';

import { useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { Trans } from '@kit/ui/trans';
import { useTranslation } from 'react-i18next';
import { useTrialStatus } from '../hooks/use-trial-status';

interface GlobalTrialGuardProps {
  accountId: string;
  accountSlug: string;
  children: React.ReactNode;
}

export function GlobalTrialGuard({ accountId, accountSlug, children }: GlobalTrialGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { t } = useTranslation('trials');
  const { trialInfo, isLoading } = useTrialStatus({ accountId });

  useEffect(() => {
    // Don't redirect while loading
    if (isLoading) return;

    // Don't redirect if no trial info (personal accounts)
    if (!trialInfo) return;

    // Don't redirect if already on billing page or auth pages
    if (pathname.includes('/billing') || pathname.includes('/auth')) return;

    // Don't redirect if trial is active or converted
    if (trialInfo.isActive || trialInfo.isConverted) return;

    // Redirect to billing page if trial is expired
    if (trialInfo.isExpired) {
      console.log(t('trialExpiredRedirect'));
      router.replace(`/home/<USER>/billing`);
      return;
    }

    // Also redirect if trial status is inactive (shouldn't happen for team accounts, but safety check)
    if (trialInfo.status === 'inactive') {
      console.log(t('trialInactiveRedirect'));
      router.replace(`/home/<USER>/billing`);
      return;
    }
  }, [trialInfo, isLoading, pathname, router, accountSlug]);

  // Show loading state while checking trial status
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Block access if trial is expired and not on billing page
  if (trialInfo?.isExpired && !pathname.includes('/billing')) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen space-y-4 text-center p-8">
        <div className="text-6xl">⏰</div>
        <h1 className="text-2xl font-bold">
          <Trans i18nKey="trials:trialExpiredBlocked" />
        </h1>
        <p className="text-muted-foreground max-w-md">
          <Trans i18nKey="trials:trialExpiredBlockedMessage" values={{ period: 7 }} />
        </p>
        <button
          onClick={() => router.push(`/home/<USER>/billing`)}
          className="px-6 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
        >
          <Trans i18nKey="trials:upgradeNowButton" />
        </button>
      </div>
    );
  }

  return <>{children}</>;
}
