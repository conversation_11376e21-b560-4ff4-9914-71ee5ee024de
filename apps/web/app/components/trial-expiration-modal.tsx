'use client';

import { useState } from 'react';
import { Crown, Clock, AlertTriangle, CheckCircle, X } from 'lucide-react';

import { Button } from '@kit/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@kit/ui/dialog';
import { cn } from '@kit/ui/utils';
import { Trans } from '@kit/ui/trans';
import { useTranslation } from 'react-i18next';

import { type TrialInfo } from '../hooks/use-trial-status';
import { Progress } from '@radix-ui/react-progress';

interface TrialExpirationModalProps {
  trialInfo: TrialInfo;
  isOpen: boolean;
  onClose: () => void;
  onUpgrade: () => void;
  onDismiss?: () => void;
  variant?: 'warning' | 'expired' | 'reminder';
}

export function TrialExpirationModal({
  trialInfo,
  isOpen,
  onClose,
  onUpgrade,
  onDismiss,
  variant = 'warning',
}: TrialExpirationModalProps) {
  const { t } = useTranslation('trials');
  const [isDismissed, setIsDismissed] = useState(false);

  const handleDismiss = () => {
    setIsDismissed(true);
    onDismiss?.();
    onClose();
  };

  const getModalContent = () => {
    switch (variant) {
      case 'expired':
        return {
          icon: AlertTriangle,
          iconColor: 'text-red-500',
          iconBg: 'bg-red-100 dark:bg-red-500/20',
          title: t('trialExpiredHeading'),
          description: t('trialExpiredMessage', { period: 7 }),
          primaryAction: {
            label: t('actions.upgradeNow'),
            variant: 'default' as const,
            onClick: onUpgrade,
          },
          secondaryAction: null,
          showProgress: false,
        };

      case 'warning':
        return {
          icon: Clock,
          iconColor: trialInfo.daysRemaining && trialInfo.daysRemaining <= 1 ? 'text-red-500' : 'text-yellow-500',
          iconBg: trialInfo.daysRemaining && trialInfo.daysRemaining <= 1 ? 'bg-red-100 dark:bg-red-500/20' : 'bg-yellow-100 dark:bg-yellow-500/20',
          title: trialInfo.daysRemaining && trialInfo.daysRemaining <= 1 ? t('messages.trialExpiringSoon') : t('messages.trialExpiringSoon'),
          description: trialInfo.daysRemaining && trialInfo.daysRemaining > 0
            ? t('messages.trialExpiringInDays', { days: trialInfo.daysRemaining })
            : t('messages.trialExpiringInDays', { days: 0 }),
          primaryAction: {
            label: t('actions.upgradeNow'),
            variant: 'default' as const,
            onClick: onUpgrade,
          },
          secondaryAction: onDismiss ? {
            label: t('trialReminderSettings'),
            variant: 'outline' as const,
            onClick: handleDismiss,
          } : null,
          showProgress: true,
        };

      case 'reminder':
        return {
          icon: Crown,
          iconColor: 'text-blue-500',
          iconBg: 'bg-blue-100 dark:bg-blue-500/20',
          title: t('trialUpgradeBenefits'),
          description: t('trialUpgradeBenefitsDescription'),
          primaryAction: {
            label: t('actions.upgradeNow'),
            variant: 'default' as const,
            onClick: onUpgrade,
          },
          secondaryAction: {
            label: t('trialReminderSettings'),
            variant: 'outline' as const,
            onClick: onClose,
          },
          showProgress: true,
        };

      default:
        return null;
    }
  };

  const content = getModalContent();
  if (!content || isDismissed) return null;

  const { icon: Icon, iconColor, iconBg, title, description, primaryAction, secondaryAction, showProgress } = content;

  const features = [
    'Unlimited campaigns',
    'Advanced analytics',
    'Priority support',
    'Custom integrations',
    'Team collaboration',
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="text-center space-y-4">
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full">
            <div className={cn('p-3 rounded-full', iconBg)}>
              <Icon className={cn('h-6 w-6', iconColor)} />
            </div>
          </div>
          
          <div className="space-y-2">
            <DialogTitle className="text-xl font-semibold">{title}</DialogTitle>
            <DialogDescription className="text-base text-muted-foreground">
              {description}
            </DialogDescription>
          </div>
        </DialogHeader>

        {showProgress && trialInfo.status === 'active' && (
          <div className="space-y-3">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">
                <Trans i18nKey="trials:trialProgress" />
              </span>
              <span className="font-medium">
                {trialInfo.timeRemaining ? (
                  t('trialProgressTimeLeft', { 
                    days: trialInfo.timeRemaining.days, 
                    hours: trialInfo.timeRemaining.hours, 
                    minutes: trialInfo.timeRemaining.minutes 
                  })
                ) : (
                  t('trialExpiresSoon')
                )}
              </span>
            </div>
            <Progress 
              value={trialInfo.progressPercentage} 
              className={cn(
                'h-2',
                trialInfo.progressPercentage >= 80 && '[&>div]:bg-red-500',
                trialInfo.progressPercentage >= 60 && trialInfo.progressPercentage < 80 && '[&>div]:bg-yellow-500',
                trialInfo.progressPercentage < 60 && '[&>div]:bg-blue-500'
              )}
            />
          </div>
        )}

        <div className="space-y-3">
          <div className="text-sm font-medium text-center">
            <Trans i18nKey="trials:trialUpgradeBenefits" />
          </div>
          <div className="grid grid-cols-1 gap-2">
            {features.map((feature, index) => (
              <div key={index} className="flex items-center gap-2 text-sm">
                <CheckCircle className="h-4 w-4 text-green-500 shrink-0" />
                <span>{feature}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="flex flex-col gap-2 pt-2">
          <Button 
            onClick={primaryAction.onClick} 
            variant={primaryAction.variant}
            className="w-full"
          >
            <Crown className="mr-2 h-4 w-4" />
            {primaryAction.label}
          </Button>
          
          {secondaryAction && (
            <Button 
              onClick={secondaryAction.onClick} 
              variant={secondaryAction.variant}
              className="w-full"
            >
              {secondaryAction.label}
            </Button>
          )}
        </div>

        <button
          onClick={onClose}
          className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
        >
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </button>
      </DialogContent>
    </Dialog>
  );
}

interface TrialReminderModalProps {
  trialInfo: TrialInfo;
  isOpen: boolean;
  onClose: () => void;
  onUpgrade: () => void;
  onSnooze?: (hours: number) => void;
}

export function TrialReminderModal({
  trialInfo,
  isOpen,
  onClose,
  onUpgrade,
  onSnooze,
}: TrialReminderModalProps) {
  const { t } = useTranslation('trials');
  const snoozeOptions = [
    { label: t('trialReminder1Day'), hours: 1 },
    { label: t('trialReminder3Days'), hours: 4 },
    { label: t('trialReminder7Days'), hours: 24 },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-sm">
        <DialogHeader className="text-center space-y-4">
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-500/20">
            <Clock className="h-6 w-6 text-blue-500" />
          </div>
          
          <div className="space-y-2">
            <DialogTitle className="text-lg font-semibold">
              <Trans i18nKey="trials:trialReminder" />
            </DialogTitle>
            <DialogDescription className="text-sm text-muted-foreground">
              {trialInfo.daysRemaining && trialInfo.daysRemaining > 0
                ? t('trialExpiresIn', { days: trialInfo.daysRemaining })
                : t('trialActiveDescriptionSoon')
              }
            </DialogDescription>
          </div>
        </DialogHeader>

        <div className="space-y-3">
          <Button onClick={onUpgrade} className="w-full">
            <Crown className="mr-2 h-4 w-4" />
            <Trans i18nKey="trials:actions.upgradeNow" />
          </Button>
          
          {onSnooze && (
            <div className="space-y-2">
              <div className="text-xs text-center text-muted-foreground">Or remind me in:</div>
              <div className="grid grid-cols-3 gap-2">
                {snoozeOptions.map((option) => (
                  <Button
                    key={option.hours}
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      onSnooze(option.hours);
                      onClose();
                    }}
                    className="text-xs"
                  >
                    {option.label}
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
