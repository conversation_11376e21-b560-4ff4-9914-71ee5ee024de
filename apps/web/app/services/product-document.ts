'use server';

import { v4 as uuidv4 } from 'uuid';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { ProductDocument } from '~/types/product-document';
import { getLocal<PERSON><PERSON>, getDocupanda<PERSON><PERSON> } from '~/utils/api.util';

import { getDocumentWithUrls } from './storage';

const server = getSupabaseServerClient();

type LoggerContext = {
  name: string;
  userId?: string;
  companyId?: string;
  documentTitle?: string;
  fileType?: string;
  filePath?: string;
};

export async function uploadProductDocument(
  title: string,
  file: File | null,
  companyId: string,
  websiteContent?: string,
  blobUrl?: string,
) {
  const logger = await getLogger();
  const ctx: LoggerContext = {
    name: 'upload-product-document',
    documentTitle: title,
  };

  console.log("UPLOADING PRODUCT DOCUMENT", {title, file, companyId, websiteContent, blobUrl});
  logger.info(ctx, 'Uploading product document...');

  if (!websiteContent && !file && !blobUrl) {
    logger.error(ctx, 'No file, website content, or blob URL provided');
    throw new Error('No file, website content, or blob URL provided');
  }

  ctx.companyId = companyId;
  let document;

  try {
    if (blobUrl) {
      // Handle blob URL upload
      logger.info(ctx, 'Processing document from blob URL');
      
      // Extract filename and extension from blob URL
      const urlParts = blobUrl.split('/');
      const fileName = urlParts[urlParts.length - 1] || 'document.pdf';
      const fileExt = fileName.split('.').pop()?.toLowerCase() || 'pdf';

      ctx.fileType = fileExt;
      ctx.filePath = blobUrl;

      // Check if file is PDF or DOCX
      if (fileExt !== 'pdf' && fileExt !== 'docx') {
        logger.error(ctx, 'Invalid file type - only PDFs and DOCX files are supported');
        throw new Error('Please upload PDFs or DOCX files');
      }

      logger.info(ctx, 'Extracting text from blob document');
      
      // Process document directly instead of calling API route
      // Download the file from the blob URL
      logger.info(ctx, `Downloading file from blob URL: ${blobUrl}`);
      const fileResponse = await fetch(blobUrl);
      
      if (!fileResponse.ok) {
        logger.error(ctx, 'Failed to download file from blob URL');
        throw new Error('Failed to download file from blob URL');
      }

      // Convert to base64 for DocuPanda
      const arrayBuffer = await fileResponse.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      const base64File = buffer.toString('base64');

      const docupandaApi = getDocupandaApi();

      // Upload to DocuPanda
      logger.info(ctx, 'Uploading file to DocuPanda');
      const uploadResponse = await docupandaApi.post('/document', {
        document: {
          file: {
            contents: base64File,
            filename: fileName,
          },
        },
      });

      const { documentId } = uploadResponse.data;
      logger.info(ctx, `File uploaded, documentId: ${documentId}`);

      // Wait for processing to complete
      let documentStatus = 'processing';
      let documentResult: any = null;
      while (documentStatus === 'processing') {
        logger.info(ctx, 'Checking document processing status');
        const statusResponse = await docupandaApi.get(`/document/${documentId}`);
        const statusData = statusResponse.data;
        documentStatus = statusData.status;
        if (statusData.status === 'completed') {
          documentResult = statusData.result;
        }
        await new Promise((resolve) => setTimeout(resolve, 3000)); // Wait 3 seconds between checks
      }

      const extractedText = JSON.stringify(documentResult?.text || '');
      document = {
        title,
        file_path: blobUrl, // Store blob URL instead of Supabase storage path
        company_id: companyId,
        content: extractedText,
        file_type: fileExt,
      } as ProductDocument;
    } else if (file) {
      // Legacy file upload path (keeping for backward compatibility)
      const fileExt = file.name.split('.').pop();
      const fileName = `${title.toLowerCase().replace(/[^a-z0-9]/g, '-')}-${uuidv4()}.${fileExt}`;
      const filePath = `${companyId}/productDocuments/${fileName}`;

      ctx.fileType = fileExt;
      ctx.filePath = filePath;

      // Check if file is PDF
      if (fileExt?.toLowerCase() !== 'pdf') {
        logger.error(ctx, 'Invalid file type - only PDFs are supported');
        throw new Error('Please Upload PDFs at this time');
      }

      logger.info(ctx, `Uploading file to storage path: ${filePath}`);

      const { error: uploadError } = await server.storage
        .from('company-products')
        .upload(filePath, file);

      if (uploadError) {
        logger.error(
          { ...ctx, error: uploadError },
          'Failed to upload file to storage',
        );
        throw uploadError;
      }

      logger.info(ctx, 'Extracting text from PDF document');
      // Create form data and append file
      const formData = new FormData();
      formData.append('file', file);

      // Use getLocalApi for server-side API call
      const response = await getLocalApi('', 'multipart/form-data').post(
        '/documents',
        formData,
      );

      if (response.status !== 200) {
        throw new Error('Failed to extract text from file');
      }

      const extractedText = response.data;
      document = {
        title,
        file_path: filePath,
        company_id: companyId,
        content: extractedText,
        file_type: fileExt,
      } as ProductDocument;
    } else {
      logger.info(ctx, 'Creating document from website content');
      document = {
        title,
        company_id: companyId,
        content: websiteContent,
        file_type: 'Website',
        file_path: 'Website',
      } as ProductDocument;
    }

    console.log("DOCUMENT", document);
    logger.info(ctx, 'Inserting document into database');
    const { data: uploadedDocument, error } = await server
      .from('product_documents')
      .insert(document)
      .select();

    if (error) {
      logger.error(
        { ...ctx, error },
        'Failed to insert document into database',
      );
      throw error;
    }

    logger.info(
      { ...ctx, documentId: uploadedDocument[0]?.id },
      'Successfully uploaded product document',
    );
    return uploadedDocument;
  } catch (error) {
    logger.error(
      { ...ctx, error },
      'Unexpected error uploading product document',
    );
    throw error;
  }
}

export async function getProductDocuments(companyId: string) {
  const logger = await getLogger();
  const ctx: LoggerContext = {
    name: 'get-product-documents',
  };

  logger.info(ctx, 'Fetching product documents...');

  try {

    ctx.companyId = companyId;

    logger.info(ctx, `Fetching documents for company: ${companyId}`);
    const { data: documents, error } = await server
      .from('product_documents')
      .select('*')
      .eq('company_id', companyId)
      .order('created_at', { ascending: false });

    if (error) {
      logger.error({ ...ctx, error }, 'Failed to fetch documents');
      throw new Error('Could not fetch documents');
    }

    logger.info(
      { ...ctx, documentCount: documents?.length },
      'Getting public URLs for documents',
    );
    // Get public URLs for documents
    const documentsWithUrls = await getDocumentWithUrls(
      documents as ProductDocument[],
    );

    logger.info(
      { ...ctx, documentCount: documentsWithUrls.length },
      'Successfully fetched product documents',
    );
    return documentsWithUrls;
  } catch (error) {
    logger.error(
      { ...ctx, error },
      'Unexpected error fetching product documents',
    );
    throw error;
  }
}

export async function updateProductDocumentTitle(
  documentId: string,
  newTitle: string,
) {
  const logger = await getLogger();
  const ctx: LoggerContext = {
    name: 'update-product-document-title',
    documentTitle: newTitle,
  };

  logger.info(ctx, 'Updating product document title...');

  try {
    const { data: updatedDocument, error } = await server
      .from('product_documents')
      .update({ title: newTitle })
      .eq('id', documentId)
      .select()
      .single();

    if (error) {
      logger.error(
        { ...ctx, error, documentId },
        'Failed to update document title',
      );
      throw new Error('Could not update document title');
    }

    logger.info(
      { ...ctx, documentId },
      'Successfully updated product document title',
    );
    return updatedDocument;
  } catch (error) {
    logger.error(
      { ...ctx, error, documentId },
      'Unexpected error updating product document title',
    );
    throw error;
  }
}

export async function deleteProductDocument(documentId: string) {
  const logger = await getLogger();
  const ctx: LoggerContext = {
    name: 'delete-product-document',
  };

  logger.info(ctx, 'Deleting product document...');

  try {
    // First get the document to check if it has a file to delete from storage
    const { data: document, error: fetchError } = await server
      .from('product_documents')
      .select('file_path')
      .eq('id', documentId)
      .single();

    if (fetchError) {
      logger.error(
        { ...ctx, error: fetchError, documentId },
        'Failed to fetch document for deletion',
      );
      throw new Error('Could not find document to delete');
    }

    // Delete the file from storage if it exists and is not a website document
    if (document.file_path && document.file_path !== 'Website') {
      const { error: storageError } = await server.storage
        .from('company-products')
        .remove([document.file_path]);

      if (storageError) {
        logger.error(
          { ...ctx, error: storageError, filePath: document.file_path },
          'Failed to delete file from storage',
        );
        // Continue with database deletion even if storage deletion fails
      }
    }

    // Delete the document from the database
    const { error: deleteError } = await server
      .from('product_documents')
      .delete()
      .eq('id', documentId);

    if (deleteError) {
      logger.error(
        { ...ctx, error: deleteError, documentId },
        'Failed to delete document from database',
      );
      throw new Error('Could not delete document');
    }

    logger.info(
      { ...ctx, documentId },
      'Successfully deleted product document',
    );
    return { success: true };
  } catch (error) {
    logger.error(
      { ...ctx, error, documentId },
      'Unexpected error deleting product document',
    );
    throw error;
  }
}
